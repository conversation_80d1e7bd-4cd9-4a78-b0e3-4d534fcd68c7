const {ResponseData} = require("../utils/ResponseData");
const {Controller} = require("../../core");
class OtherProjectDayWorkController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }


    /**
     * 获取默认计日工数据
     */
    getDefaultOtherProjectDayWork(){
        const res = this.service.otherProjectDayWorkService.getDefaultOtherProjectDayWork();
        return ResponseData.success(res);
    }

    getOtherProjectDayWork(){
        const res = this.service.otherProjectDayWorkService.getOtherProjectDayWork();
        return ResponseData.success(res);
    }

    /**
     * 计日工操作
     * @param arg
     */
    async otherProjectDayWork(arg,redo="计日工 {columnTitle} 由【{oldValue}】修改为【{newValue}】"){
        const res = await this.service.otherProjectDayWorkService.otherProjectDayWork(arg);

        if (arg.operateType !==1){

            await this.service.management.sycnTrigger("unitDeChange");
            await this.service.management.trigger("itemChange");
        }
        return ResponseData.success(res);
    }


}

OtherProjectDayWorkController.toString = () => '[class OtherProjectDayWorkController]';
module.exports = OtherProjectDayWorkController;