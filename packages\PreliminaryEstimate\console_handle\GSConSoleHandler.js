const {ConSoleCommonHandler} = require("../../../electron/console_handle/ConSoleCommonHandler");
const EE = require("../../../core/ee");
const {UPCContext} = require("../../../electron/unit_price_composition/core/UPCContext");
const {PricingFileWriteUtils} = require("../../../electron/utils/PricingFileWriteUtils");
const {ProjectFileUtils} = require("../../../common/ProjectFileUtils");
const {getOperator} = require("../core/tools/fileOperator/FileOperator");
const {File_TYPE_YGS} = require("../constants/FileOperatorType");
const YGSOperator = require("../core/tools/fileOperator/YGSOperator");
const {Snowflake} = require("../utils/Snowflake");
const {ObjectUtil} = require("../../../common/ObjectUtil");
const ProjectTypeConstants = require("../constants/ProjectTypeConstants");
const FunctionTypeConstants = require("../constants/FunctionTypeConstants");


class GSConSoleHandler extends ConSoleCommonHandler{

    constructor({path}) {
        super({path});
        this.path = path;
    }

    /**
     *文件数据处理
     */
    async fileDataHandle(obj){
        return obj;
    }

    async after(win,obj) {
        //用户的打开历史记录列表数据处理
        ProjectFileUtils.writeUserHistoryListFile(obj);
    }

    /**
     * 打开本地文件
     * @param params
     */
    async openLocalObj(obj) {
        let projectDomain = null;
        let projectRoot = null;

        let objStr = JSON.stringify(obj);
        let projectRootObj = await YGSOperator.getConstructRoot(objStr);
        if (obj.path !== projectRootObj.path) {
            //项目路径改变，重新刷新项目constructId
            // let jsonObj = ObjectUtils.stringifyComplexObject(YGLJOperator.prepareContent(projectDomain));
            let data = JSON.stringify(obj);
            //  获取项目id
            let oldConstructId = await YGSOperator.getFirstConstructId(data);
            let newConstructId = Snowflake.nextId();
            const regex = new RegExp(oldConstructId, 'g');
            data = data.replace(regex, newConstructId);
            // 尝试解析 JSON 数据
            obj = JSON.parse(data);

            projectDomain = await YGSOperator.destructuringFile(obj);
            projectRoot = projectDomain.getRoot();
        } else {
            projectDomain = await YGSOperator.destructuringFile(obj);
            projectRoot = projectDomain.getRoot();
        }

        let constructId = projectDomain.getRoot().sequenceNbr;
        let unitProjects = projectDomain.getProject(item => item.type === ProjectTypeConstants.PROJECT_TYPE_UNIT);
        let {service} = EE.app;
        //todo 查询文件是否有小数点配置数据，无的话取默认重新计算
        if (ObjectUtil.isEmpty(projectDomain.functionDataMap.get(FunctionTypeConstants.PROJECT_PRECISION_SETTING))) {
            await service.PreliminaryEstimate.gsProjectService.repeatCalPrecision(constructId, unitProjects);
        }


        projectRoot.path = obj.path;

        //获取选中的路径
        let filePath = projectRoot.path;
        //无需返回
        let win = await service.PreliminaryEstimate.gsAppService.newEstimateProject(projectRoot.sequenceNbr);

        // //打开后就保存，保证filepath的存在
        // if (!projectRoot.path) {
        //     projectRoot.path = filePath;
        //     let operate = getOperator(File_TYPE_YGS);
        //     await operate.saveByFullPath(projectDomain, filePath);
        // }
        projectRoot.path = filePath;
        let operate = getOperator(File_TYPE_YGS);
        await operate.saveByFullPath(projectDomain, filePath);

        //窗口事件定义
        // this.winEventInit(win, projectRoot);
        projectRoot.constructName = projectRoot.name;
        await this.after(win, projectRoot);
        return win;
    }

}

module.exports = {
    GSConSoleHandler: GSConSoleHandler
}
