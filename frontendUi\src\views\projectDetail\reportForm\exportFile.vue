<!--
 * @Descripttion: 导出项目
 * @Author: sunchen
 * @Date: 2023-08-04 10:40:22
 * @LastEditors: renmingming <EMAIL>
 * @LastEditTime: 2025-08-06 14:42:36
-->
<template>
  <common-modal
    className="dialog-comm tree-dialog"
    width="auto"
    v-model:modelValue="dialogVisible"
    title="导出项目"
    @cancel="cancel"
    @close="cancel">
    <div class="tree-content-wrap">
      <div class="dialog-content">
        <div class="dialog-content-left">
          <div class="title-wrap">
            <span class="title">导出文件类型</span>
            <a-select
              ref="select"
              style="width: 200px"
              v-model:value="useType"
              @change="changeType">
              <a-select-option :value="i.type" v-for="i of list">{{ i.name }}</a-select-option>
            </a-select>
            <!-- <a-radio-group
              size="small"
              v-model:value="useType"
              @change="changeType"
              name="radioGroup"
            >
              <a-radio :value="i.type" v-for="i of list" class="radio">
                <span class="label">{{ i.name }}</span>
              </a-radio>
            </a-radio-group> -->
          </div>
          <div class="list">
            <a-spin :spinning="treeLoading">
              <a-tree
                v-if="treeData"
                checkable
                show-line
                :tree-data="treeData"
                :fieldNames="{
                  children: 'childrenList',
                  title: 'headLine',
                  key: 'id',
                }"
                v-model:expandedKeys="expandedKeys"
                v-model:checkedKeys="checkedKeys"
                @select="select">
                <template #switcherIcon="{ switcherCls, children }">
                  <down-outlined :class="switcherCls" />
                </template>
              </a-tree>
            </a-spin>
          </div>
        </div>
        <div class="dialog-content-right" v-if="['excel', 'pdf'].includes(useTabType)">
          <div class="head">
            <a-button type="primary" @click="setOutputSetting">导出设置</a-button>
          </div>
          <div class="right-box">
            <div class="title">其他设置：</div>
            <div class="btn-list">
              <a-button :disabled="!selectTree" @click="moveTreeRow('up')">上移</a-button>
              <a-button :disabled="!selectTree" @click="moveTreeRow('dwon')">下移</a-button>
              <a-button :disabled="!checkedKeys.length" @click="setTreeName('on')">
                选中同名报表
              </a-button>
              <a-button :disabled="!selectTree" @click="setTreeName('off')">
                取消选择同名报表
              </a-button>
            </div>
            <div class="pager-setting">
              <a-checkbox v-model:checked="exportPageConfig.isStart">连码导出</a-checkbox>
              <div class="item">
                起始页&nbsp;&nbsp;
                <a-input-number
                  v-model:value="exportPageConfig.startPage"
                  :disabled="!exportPageConfig.isStart"
                  :precision="0"
                  :min="1"
                  @blur="startPageBlur" />
              </div>
              <a-checkbox
                :disabled="!exportPageConfig.isStart"
                v-model:checked="exportPageConfig.isTotal">
                自定义总页数
              </a-checkbox>
              <div class="item">
                <a-input-number
                  :precision="0"
                  v-model:value="exportPageConfig.totalPage"
                  :disabled="!exportPageConfig.isTotal"
                  :min="1" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="group-list">
        <a-radio-group v-model:value="dataStatus" @change="changeCheck">
          <a-radio value="all">全部</a-radio>
          <a-radio value="part">取消全部</a-radio>
        </a-radio-group>
      </div>
      <div class="footer-btn-list">
        <a-button @click="cancel">取消</a-button>
        <a-button
          type="primary"
          @click="save"
          :loading="submitLoading"
          :disabled="!checkedKeys.length">
          确定
        </a-button>
      </div>
    </div>
  </common-modal>

  <common-modal
    v-model:modelValue="SettingStatus"
    className="dialog-comm"
    title="导出设置"
    width="auto">
    <div class="radio-list">
      <div class="type-box">
        <div class="radio-title">
          <icon-font type="icon-zhaobiaoleixing" class="icon" />
          <span>页眉页脚导出设置</span>
        </div>
        <a-checkbox v-model:checked="SettingConfig.exportPageEyeBrowPageFoot">页眉页脚</a-checkbox>
      </div>

      <div class="type-box" v-if="useTabType === 'excel'">
        <div class="radio-title">
          <icon-font type="icon-changjia" class="icon" />
          <span>批量导出Excel选项</span>
        </div>
        <a-radio-group v-model:value="SettingConfig.batchExport">
          <a-radio :style="radioStyle" value="excelWithLevel">
            单个Excel模式
            <a-popover placement="topLeft">
              <template #content>
                <p>所有类型的报表根据层级导出到一个Excel中，不同的报表用不同的sheet表示</p>
              </template>
              <icon-font type="icon-tishineirong" class="icon" />
            </a-popover>
          </a-radio>
          <a-radio :style="radioStyle" value="excelWithAll">
            单个Excel模式（所有项目报表）
            <a-popover placement="topLeft">
              <template #content>
                <p>项目内所有的报表导出到一个Excel中，不同的报表用不同的sheet表示</p>
              </template>
              <icon-font type="icon-tishineirong" class="icon" />
            </a-popover>
          </a-radio>
          <a-radio :style="radioStyle" value="excelWithSingleSheet">
            多个Excel模式
            <a-popover placement="topLeft">
              <template #content>
                <p>每一张报表新建一个Excel</p>
              </template>
              <icon-font type="icon-tishineirong" class="icon" />
            </a-popover>
          </a-radio>
        </a-radio-group>
      </div>

      <div class="type-box">
        <div class="radio-title">
          <icon-font type="icon-zhaobiaoleixing" class="icon" />
          <span>导出报表计税方式</span>
        </div>
        <a-checkbox v-model:checked="SettingConfig.containsTaxCalculation">
          导出报表包含计税方式
        </a-checkbox>
      </div>
    </div>
    <div class="footer-btn-list">
      <a-button @click="CancelDialog">取消</a-button>
      <a-button type="primary" @click="saveSetting()">确定</a-button>
    </div>
  </common-modal>
</template>
<script setup>
import { message } from 'ant-design-vue';
import { ref, reactive, watch, nextTick, markRaw, defineExpose, onBeforeUnmount } from 'vue';
import { useRoute } from 'vue-router';
import csProject from '@/api/csProject';
import projectDetailApi from '@/api/projectDetail';
import systemApi from '@/api/system';
import { DownOutlined } from '@ant-design/icons-vue';
import { projectDetailStore } from '@/store/projectDetail';
import XEUtils from 'xe-utils';

const store = projectDetailStore();
const emit = defineEmits(['handleOk', 'refreshTree']);
const route = useRoute();

const treeData = ref(null);
const submitLoading = ref(false);
const dialogVisible = ref(false);
const checkedKeys = ref([]); // 选中的数据，为了后续要处理其他字段
const dataStatus = ref(null); // 全选，单选
const useType = ref('招标项目报表'); // 弹窗里的文件类型
const list = ref([]); //导出文件类型列表
const useTabType = ref(); // 页面主入口导出文件类型，excel，pdf,xml
let treeLoading = ref(false);
let timer = ref(null);
let expandedKeys = ref([]);

const radioStyle = reactive({
  fontSize: '14px',
  display: 'block',
});
let exportPageConfig = reactive({
  isStart: false,
  startPage: '',
  totalPage: '',
  isTotal: false,
});
const startPageBlur = () => {
  if (exportPageConfig.isStart && !exportPageConfig.startPage) {
    exportPageConfig.startPage = 1;
  }
};
let prevUseType = ref('招标项目报表');
/**
 * 切换了导出文件类型
 */
const changeType = XEUtils.debounce(e => {
  console.log(useType.value, e);
  updateSelected();
  treeData.value = null;
  expandedKeys.value = [];
  checkedKeys.value = [];
  prevUseType.value = useType.value;
  getTreeList();
}, 300);

// 更新选择状态
const updateSelected = () => {
  const data = JSON.parse(JSON.stringify(treeData.value));
  const list = flattenTree(data)[0];
  const params = {
    lanMuName: prevUseType.value,
    params: JSON.parse(JSON.stringify(list)),
  };
  console.log('更新选中数据', params);
  csProject.saveSelected(params).then(res => {
    console.log('更新选中结果', res);
  });
};

let allSheetList = ref([]);
/**
 * 获取所有选中的id
 */
const getTreeChecked = childrenList => {
  let checkedKeys = [];
  let sheetList = [];
  loopTree(childrenList);
  function loopTree(childrenList) {
    for (let child of childrenList) {
      if (child.selected) checkedKeys.push(child.id);
      if (child.childrenList && child.childrenList.length) {
        loopTree(child.childrenList);
      }
      if (child.projectLevel) {
        sheetList.push(child);
      }
    }
  }
  allSheetList.value = sheetList;
  return checkedKeys;
};

/**
 * 获取树数据
 */
const getTreeList = () => {
  treeLoading.value = true;
  csProject
    .exportProjectTree(route.query.constructSequenceNbr, useType.value, useTabType.value)
    .then(res => {
      console.log('数数据', res.result);
      if (res.status === 200 && res.result) {
        removeExcelDataTemplate(res.result);
        treeData.value = [res.result];
        expandedKeys.value.push(treeData.value[0].id);
        checkedKeys.value = getTreeChecked(res.result?.childrenList || []);
        if (checkedKeys.value.length === allSheetList.value.length) {
          dataStatus.value = 'all';
        } else {
          dataStatus.value = '';
        }
        console.log('🌶exportFile.vue|306====>', checkedKeys.value);
        // for (const item of treeData.value[0].childrenList) {
        //   expandedKeys.value.push(item.id)
        // }
      }
    })
    .finally(() => {
      nextTick(() => {
        treeLoading.value = false;
      });
    });
};

function removeExcelDataTemplate(node) {
  if (node) {
    // 删除当前节点的 excelDataTemplate 字段
    delete node.excelDataTemplate;

    // 如果当前节点有 childrenList，递归处理每个子节点
    if (node.childrenList) {
      if (Array.isArray(node.childrenList)) {
        node.childrenList.forEach(child => removeExcelDataTemplate(child));
      } else {
        removeExcelDataTemplate(node.childrenList);
      }
    }
  }
}

// 导出获取类型
const gettype = () => {
  list.value = [
    {
      type: '招标项目报表',
      name: '招标项目',
    },
    {
      type: '投标项目报表',
      name: '投标项目',
    },
    {
      type: '工程量清单报表',
      name: '工程量清单',
    },
    {
      type: '测评响应招标项目报表',
      name: '测评响应招标项目',
    },
    {
      type: '测评响应投标项目报表',
      name: '测评响应投标项目',
    },
    {
      type: '测评响应工程量清单报表',
      name: '测评响应工程量清单',
    },
  ];

  const mappingList = {
    ZB: 0,
    TB: 1,
    DW: 2,
  };

  let typeIndex = mappingList[store.projectType] || 0;
  useType.value = list.value[typeIndex].type;
  prevUseType.value = useType.value;

  getTreeList();
};

const cancel = () => {
  updateSelected();
  treeData.value = null;
  useType.value = 1;
  dialogVisible.value = false;
  checkedKeys.value = [];
  expandedKeys.value = [];
  dataStatus.value = null;
  setTimeout(() => {
    emit('refreshTree');
  }, 200);
};

/**
 * 点击全选与非全选
 */
const changeCheck = () => {
  if (dataStatus.value === 'all') {
    checkedKeys.value = [treeData.value[0]?.id];
    checkedKeys.value = flattenTree(treeData.value).map(item => item.id);
  } else {
    checkedKeys.value = [];
  }
};

const save = async () => {
  if (!checkedKeys.value || !checkedKeys.value.length) {
    message.error('请选择要导出的工程');
    return;
  }
  if (checkedKeys.value.length === allSheetList.value.length) {
    dataStatus.value = 'all';
  } else {
    dataStatus.value = '';
  }
  try {
    submitLoading.value = true;
    // debugger;
    updateSelected();
    const list = flattenTree(treeData.value)[0];
    const params = {
      lanMuName: useType.value,
      params: JSON.parse(JSON.stringify(list)),
      startPage: exportPageConfig.isStart ? exportPageConfig.startPage || 1 : '',
      totalPage: exportPageConfig.isTotal ? exportPageConfig.totalPage : '',
    };
    console.log('导出excel：', params, useTabType.value);
    let apiName = useTabType.value === 'pdf' ? 'exportPdfFile' : 'exportExcel';
    const res = await csProject[apiName](params);
    console.log('🚀 ~ file: exportFile.vue:185 ~ save ~ res:', res);
    if (res?.result) {
      message.success('导出成功！');
      cancel();
    }
  } catch (error) {
    console.error(error);
  } finally {
    submitLoading.value = false;
  }
};

/**
 * 将树结构里面的某个字段与选中的数据进行关联
 * @param {*} treeList
 */
const flattenTree = treeList => {
  const result = [];

  function traverse(node) {
    result.push(node); // 将当前节点添加到结果数组中

    if (node.childrenList && node.childrenList.length > 0) {
      // 递归处理子节点
      for (let i = 0; i < node.childrenList.length; i++) {
        const data = node.childrenList[i];
        data.selected = checkedKeys.value.includes(data.id) || ['all'].includes(dataStatus.value);
        traverse(data);
      }
    }
  }
  // 遍历树列表中的每个根节点
  for (let i = 0; i < treeList.length; i++) {
    const root = treeList[i];
    root.selected = checkedKeys.value.includes(root.id) || ['all'].includes(dataStatus.value);
    traverse(root);
  }

  return result;
};

/**
 *
 * @param {*} type 点击类型
 */
const open = type => {
  treeLoading.value = true;
  prevUseType.value = type;
  useTabType.value = type;
  dialogVisible.value = true;
  gettype();
  submitLoading.value = false;
};

onBeforeUnmount(() => {
  console.log('销毁了');
  // debugger
  treeLoading.value = false;
  timer.value = null;
});

let selectTree = ref(null);
let selectParent = ref(null);

const select = (selectedKeys, e) => {
  selectTree.value = selectedKeys;
  if (e.node) {
    selectParent.value = {
      node: e.node.dataRef,
      children: e.node.parent?.node.childrenList,
    };
  } else {
    selectParent.value = null;
  }
};

// 导出设置
let SettingStatus = ref(false);
let SettingConfig = reactive({
  exportPageEyeBrowPageFoot: false,
  batchExport: 'excelWithLevel',
  containsTaxCalculation: false,
});
const setOutputSetting = () => {
  queryConstructProjectMessageColl();
  SettingStatus.value = true;
};
// 设置数据回显
const queryConstructProjectMessageColl = () => {
  let apiData = {
    constructId: route.query.constructSequenceNbr,
  };
  projectDetailApi.queryConstructProjectMessageColl(apiData).then(res => {
    if (res.status === 200 && res.result) {
      console.log(res.result, '设置回显');
      Object.assign(SettingConfig, res.result.exportConfig);
    }
  });
};
const CancelDialog = () => {
  SettingStatus.value = false;
  SettingConfig.exportPageEyeBrowPageFoot = false;
  SettingConfig.batchExport = 'excelWithLevel';
  SettingConfig.containsTaxCalculation = false;
};

const saveSetting = () => {
  const params = {
    constructId: route.query.constructSequenceNbr,
    ...JSON.parse(JSON.stringify(SettingConfig)),
  };
  console.log(params, '设置参数');
  csProject.exportConfiguration(params).then(res => {
    console.log(res);
    CancelDialog();
  });
};

const setTreeName = (type = 'on') => {
  let data = null;
  if (checkedKeys.value.length || selectTree.value?.length) {
    let initData = XEUtils.clone(treeData.value, true);
    data = XEUtils.toTreeArray(initData, {
      children: 'childrenList',
      clear: true,
    });
  }
  if (type == 'on' && checkedKeys.value.length) {
    // treeData 根据id,在treeData里找到对应节点
    let selectValue = [];
    // 找到选中的等级以及名字
    data.forEach(item => {
      if (checkedKeys.value.includes(item.id)) {
        selectValue.push(item);
      }
    });

    let checkedId = [];
    data.forEach(item => {
      // 判断headLine和projectLevel是不是和selectValue的值相同
      selectValue.forEach(e => {
        if (e.headLine == item.headLine && e.projectLevel == item.projectLevel) {
          checkedId.push(item.id);
        }
      });
    });
    checkedKeys.value = checkedId;
  }

  if (type == 'off') {
    let info = data.find(i => i.id == selectTree.value[0]);
    let checkedId = [];
    data.forEach(item => {
      if (info.headLine.trim() == item.headLine.trim() && info.projectLevel == item.projectLevel) {
        checkedId.push(item.id);
      }
    });
    checkedKeys.value = checkedKeys.value.filter(key => !checkedId.includes(key));
  }
};

// 上下移动
const moveTreeRow = type => {
  moveNode(treeData.value, selectTree.value[0], type);
};

const moveNode = (tree, nodeId, direction) => {
  const { node, children } = selectParent.value;

  if (!node || !children) {
    return tree;
  }

  const siblings = children;
  const index = siblings.findIndex(item => item.id === nodeId);
  if (index === -1) {
    return tree;
  }

  const newIndex = direction === 'up' ? index - 1 : index + 1;

  if (newIndex < 0 || newIndex >= siblings.length) {
    return tree;
  }

  siblings.splice(index, 1);
  siblings.splice(newIndex, 0, node);

  return tree;
};

defineExpose({
  open,
  cancel,
});
</script>

<style lang="scss" scoped>
.tree-content-wrap {
  width: 825px;
  height: 60vh;
  display: flex;
  flex-direction: column;
}
.dialog-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  .dialog-content-left {
    background: rgba(250, 250, 250, 0.39);
    border: 1px solid #e1e1e1;
    display: flex;
    flex-direction: column;
    border-radius: 2px;
    flex: 1;
    overflow: hidden;
    &:hover {
      overflow: auto;
    }
  }
  .dialog-content-right {
    margin-left: 10px;
    width: 158px;
    background: #fafafa;
    border: 1px solid #e1e1e1;
    .right-box {
      height: calc(100% - 42px);
      overflow: auto;
    }
    .head {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 7px 0;
      background-color: #eaeaea;
    }
    .title {
      padding: 14px 19px;
      font-size: 14px;
      color: #2a2a2a;
    }
    ::v-deep(.ant-btn) {
      width: 120px;
    }
    .btn-list {
      text-align: center;
      ::v-deep(.ant-btn) {
        font-size: 12px;
        margin-bottom: 10px;
      }
    }
    .pager-setting {
      padding: 10px 19px;
      ::v-deep(.ant-input-number) {
        flex: 1;
        input {
          height: 22px;
        }
      }
      .item {
        display: flex;
        align-items: center;
        white-space: nowrap;
        margin: 10px 0;
      }
    }
  }

  .title-wrap {
    padding: 7px 13px;
    background-color: #eaeaea;

    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      font-size: 14px;
      font-weight: 600;
      color: #333333;
    }
    :deep(.ant-radio-group) {
      .ant-radio-wrapper {
        border-radius: 3px;
        padding: 4px 14px;
        color: #606060;
        background-color: #fff;
        border: 1px solid #d9d9d9;
      }
      .ant-radio-wrapper-checked {
        color: #287cfa;
        border: 1px solid #287cfa;
      }
      .label {
        font-size: 12px;
        user-select: none;
      }
    }
  }
  .list {
    flex: 1;
    padding: 16px 18px;
    background-color: #fafafa;
    overflow: hidden;
    &:hover {
      overflow: auto;
    }
    :deep(.ant-spin) {
      height: 100%;
      width: 100%;
    }

    :deep(.ant-tree) {
      background-color: #fafafa;
      .ant-tree-switcher-noop {
        opacity: 0;
      }
      .ant-tree-switcher {
        background-color: transparent;
      }
    }
  }
}

.group-list {
  margin: 14px 0 30px 16px;
}

.radio-list {
  width: 600px;
  .type-box {
    background: #ffffff;
    border: 1px solid #d9d9d9;
    margin-bottom: 11px;
    border-radius: 2px;
    padding: 14px 13px;
  }
  .radio-title {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    span {
      font-weight: 400;
      font-size: 13px;
      color: #287cfa;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-left: 4px;
    }
  }
}
</style>
