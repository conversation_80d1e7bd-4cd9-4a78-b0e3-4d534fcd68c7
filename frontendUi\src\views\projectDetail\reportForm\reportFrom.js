/*
 * @Descripttion:
 * @Author: sunchen
 * @Date: 2023-08-25 14:43:38
 * @LastEditors: sunchen
 * @LastEditTime: 2024-07-22 10:25:17
 */
import { reactive } from 'vue';

export const globalData = reactive({
  useMenu: '', // 表格头部选中的
  showSchedule: false, // 是否显示进度页面
  currentInfo: null, //工程项目点击
  treeParams: {}, //获取报表左侧树
  headLine: '', //表名称
  containsTaxCalculation: false, // 标题展示计税方式
  excelDataTemplate: { id: '' },
  openEdit: false, //是否打开了设计报表弹窗
});

// 设置值
export const setLocalStorage = (value, name = 'reportForm') => {
  localStorage.setItem(name, JSON.stringify(value));
};

// 删除缓存
export const removeLocalStorage = (name = 'reportForm') => {
  localStorage.removeItem(name);
};

// 获取值
export const getLocalStorage = (name = 'reportForm') => {
  return JSON.parse(localStorage.getItem(name));
};
