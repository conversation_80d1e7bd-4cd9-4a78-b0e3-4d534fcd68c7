/*
 * @Descripttion: 关联数据
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2024-11-04 16:05:09
 * @LastEditors: k<PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-01-16 09:40:47
 */

import { ref, nextTick } from 'vue';
import xeUtils from 'xe-utils';
import { projectDetailStore } from '@/store/projectDetail';
import { proRedo } from '@/store/proRedo';
import redo from '@/api/redo';
import { ipc } from '@/utils/ipcRenderer';
import { useReversePosition } from '@/hooks/useReversePosition.js';
import data from '@/hooks/redoData';
export default {
  redoList: ref([]),
  idsMap: data.idsMap,
  childrenTabName: data.childrenTabName,
  otherProjectIndex: data.otherProjectIndex,
  specialChannel: data.specialChannel,
  /**
   * 添加localRedoList数据
   * @param {Object} data localRedoList对象
   */
  addData(channel, data, params) {
    nextTick(() => {
      const projectStore = projectDetailStore();
      const redoStore = proRedo();
      let isChildrenTab = Object.keys(this.childrenTabName).find(key => this.childrenTabName[key].includes(channel))
      let hasPidArr = ['subItemProject','measuresItem'];
      console.log('寻找对应id', params, this.getIds(params, this.idsMap),isChildrenTab);

      // 检查是否为其他项目相关接口，根据operateType设置不同的redo模板
      const otherProjectChannels = {
        'controller.otherProjectProvisionalController.otherProjectProvisional': '暂列金额',
        'controller.otherProjectController.getOtherProjectQzSp': '签证与索赔计价表',
        'controller.otherProjectDayWorkController.otherProjectDayWork': '计日工',
        'controller.otherProjectServiceCostController.otherProjectServiceCost': '总承包服务费',
        'controller.otherProjectZgjController.otherProjectZygcZgj': '专业工程暂估价',
      };

      const operateTypeNames = {
        1: '插入',
        2: '粘贴',
        3: '删除',
        4: '修改',
      };

      if (otherProjectChannels[channel] && params.operateType) {
        const moduleName = otherProjectChannels[channel];
        const operateTypeName = operateTypeNames[params.operateType];

        if (params.operateType !== 4) {
          // 非修改操作：跳过redo记录
          data.name = `${moduleName}${operateTypeName}`;
        } 
      }

      // 检查specialChannel中是否有匹配的channel
      let specialChannelId = null;
      const specialChannelMatch = this.specialChannel?.find(item =>
        item.channel.includes(channel)
      );
      if (specialChannelMatch && params[specialChannelMatch.id]) {
        specialChannelId = params[specialChannelMatch.id];
        console.log('specialChannel匹配成功:', {
          channel,
          specialChannelMatch,
          specialChannelId,
          params
        });
      } else {
        console.log('specialChannel未匹配:', {
          channel,
          specialChannelMatch,
          params,
          specialChannel: this.specialChannel
        });
      }

      // 组装临时数据对象，用于页面跳转
      let tempData = {
        ...data,
        channel,
        params: {
          ...params,
          // 确保newLine.kind被包含在params中，方便模板替换时使用
          kind: params.newLine?.kind || params.pointLine?.kind || params.kind
        },
        componentsId: projectStore.componentId,
        tabSelectName: projectStore.tabSelectName,
        levelType: projectStore.currentTreeInfo.levelType || projectStore.currentTreeInfo.type,
        specialChannelId,
        id:  this.getIds(params, this.idsMap),
        revoke: 0,
      }
      if(isChildrenTab){
        if(hasPidArr.includes(projectStore.componentId)){
          tempData.pid = projectStore.subCurrentInfo.sequenceNbr
        }
      }
      if(projectStore.componentId in this.otherProjectIndex){
        tempData.childrenTabName = projectStore.componentId;
      }
      redoStore.addData(tempData);

      console.log(
        'redoStore',
        redoStore,
        projectStore
      );
    });
  },
  addnoMatchedRedoList(data) {
    nextTick(() => {
      const redoStore = proRedo();
      redoStore.addnoMatchedRedoList({
        sequenceNbr: data.sequenceNbr,
        columnTitle: data.columnTitle,
        oldValue: data.oldValue,
        newValue: data.newValue,
        rowType: data.rowType,
        checkType:data.checkType
      });
      console.log(
        'addnoMatchedRedoList',{
          sequenceNbr: data.sequenceNbr,
          columnTitle: data.columnTitle,
          oldValue: data.oldValue,
          newValue: data.newValue,
          rowType: data.rowType,
        checkType:data.checkType
        }
      )
    });
  },
  /**
   * @description: 获取redoList需要的id
   * @param {Object} params 传入的对象
   * @param {Object} idsMap  ids映射对象
   * @return {String} id
   */
  getIds(params, idsMap) {
    for (const key in idsMap) {
      if (Object.prototype.hasOwnProperty.call(idsMap, key)) {
        const value = idsMap[key];
        if (params[key] !== undefined) {
          if (value === 'string' && params[key]) {
            return params[key];
          } else if (value === 'object' && params[key]) {
            const pointLine = params[key];
            return this.getIds(pointLine, idsMap);
          }
        }
      }
    }
    return null;
  },
  /**
   * 获取redoList数据
   * @description 通过constructSequenceNbr获取redoList数据
   */
  getList() {
    redo
      .getDoList({
        constructId: this.getUrlParam(
          window.location.href,
          'constructSequenceNbr'
        ),
      })
      .then(res => {
        nextTick(() => {
          const redoStore = proRedo();
        
        
          redoStore.addDolist(res.result);
          
          console.log('addDolist', res.result);
          console.log('redoStore', redoStore.$state);
        });
      });
  },
  /**
   * 撤销
   * @description 通过sequenceNbr撤销redoList数据
   * @param {String} id 序号
   */
  revoke(id) {
    const redoStore = proRedo();
    let item = {};
    let list = redoStore.doList?.undo;
    if (id) {
      item = list.find(item => item.sequenceNbr === id);
    } else {
      item = list[0];
    }
    console.log('撤销111', item, {
      constructId: this.getUrlParam(
        window.location.href,
        'constructSequenceNbr'
      ),
      id,
    });
    redo
      .undo({
        constructId: this.getUrlParam(
          window.location.href,
          'constructSequenceNbr'
        ),
        id,
      })
      .then(res => {
        let posData = redoStore.localRedoList.find(
          a => a.sequenceNbr === item?.sequenceNbr
        );
        console.log('getDoList', res, posData);
        this.jump(posData);
        this.getList();
      });
  },
  /**
   * 恢复
   * @description 通过sequenceNbr恢复redoList数据
   * @param {String} [id] 序号
   */
  recovery(id) {
    const redoStore = proRedo();
    let item = {};
    let list = redoStore.doList?.redo;
    if (id) {
      item = list.find(item => item.sequenceNbr === id);
    } else {
      item = list[0];
    }
    console.log('恢复', item, {
      constructId: this.getUrlParam(
        window.location.href,
        'constructSequenceNbr'
      ),
      id,
    });
    redo
      .redo({
        constructId: this.getUrlParam(
          window.location.href,
          'constructSequenceNbr'
        ),
        id,
      })
      .then(res => {
        let posData = redoStore.localRedoList.find(
          a => a.sequenceNbr === item?.sequenceNbr
        );
        console.log('getDoList', res, posData);
        this.jump(posData);
        this.getList();
      });
  },
  /**
   * 重置redo
   */
  reset() {},
  /**
   * 通过item对象，跳转到对应的tab,并定位到对应的行
   * @param {Object} item - 跳转数据对象
   * @param {String} item.sequenceNbr - 序号
   * @param {String} item.tabSelectName -  tabName
   * @param {String} item.id -  id
   * @param {Number} item.levelType -  levelType
   */
  jump(item) {
    const { linkagePosition } = useReversePosition();
    let levelMap = {
      1: 'constructId',
      2: 'singleId',
      3: 'unitId',
    };
    if (item) {
      let treeId = item[levelMap[item.levelType]];
      
      let posData = {
        treeId,
        tabMenuName: item.tabSelectName,
        rowId: item.specialChannelId || item.id
      }
      if(item.pid){
        posData.rowId = item.pid
        posData.childrenTabName = item.id
      }
      if(item.childrenTabName){
        posData.childrenTabName = item.childrenTabName
      }
      console.log('jump', item, treeId,posData);
      linkagePosition(posData);
    }
  },
  /**
   * 通过url和paramName获取url参数
   * @param {String} url url
   * @param {String} paramName 参数名称
   * @return {String | null} 参数值
   */
  getUrlParam(url, paramName) {
    const reg = new RegExp(`[?&]${paramName}=([^&#]*)`, 'i');
    const match = reg.exec(url);
    return match ? decodeURIComponent(match[1]) : null;
  },
  // 1,2,13,17,19,26,27,28,29,31,32,34,35,36,37
};
