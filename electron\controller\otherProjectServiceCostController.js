const {ResponseData} = require("../utils/ResponseData");
const {Controller} = require("../../core");
class OtherProjectServiceCostController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    getOtherProjectServiceCost(args){
        const res = this.service.otherProjectServiceCostService.getOtherProjectServiceCost(args);
        return ResponseData.success(res);
    }

    /**
     * 获取默认总承包服务费
     */
    getDefaultOtherProjectServiceCost(){
        const res = this.service.otherProjectServiceCostService.getDefaultOtherProjectServiceCost();
        return ResponseData.success(res);
    }

    async otherProjectServiceCost(args,redo="总承包服务费 {columnTitle} 由【{oldValue}】修改为【{newValue}】"){
        const res = await this.service.otherProjectServiceCostService.otherProjectServiceCost(args);

        if (args.operateType !==1){

            await this.service.management.sycnTrigger("unitDeChange");
            await this.service.management.trigger("itemChange");
        }

        return ResponseData.success(res);
    }

}

OtherProjectServiceCostController.toString = () => '[class OtherProjectServiceCostController]';
module.exports = OtherProjectServiceCostController;