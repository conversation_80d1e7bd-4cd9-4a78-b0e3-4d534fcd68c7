<!--
 * @Descripttion: 快速设计
-->
<template>
  <common-modal
    className="dialog-comm rapid-design-dialog"
    width="1000px"
    v-model:modelValue="dialogVisible"
    title="快速设计"
    @cancel="cancel"
    @close="cancel"
    :destroyOnClose="true"
    @zoom="resetPreview"
    show-zoom>
    <div class="rapid-design-content">
      <div class="aside" @click="asideClick">
        <div
          :class="{ list: true, active: item.value === asideAction }"
          v-for="item in asideList"
          :key="item.value"
          :data-value="item.value">
          {{ item.label }}
        </div>
      </div>
      <div class="content">
        <div class="list-content" v-if="asideAction === 'pageConfig'">
          <div class="box-content">
            <div class="title">纸张方向</div>
            <div class="info">
              <a-radio-group v-model:value="formData.landSpace" name="radioGroup">
                <a-radio :value="true">横板</a-radio>
                <a-radio :value="false">竖版</a-radio>
              </a-radio-group>
            </div>
          </div>
          <div class="box-content">
            <div class="title">标题设计</div>
            <div class="info">
              <RichText v-model:defaultStyle="headerStyle" :content="headTitle"></RichText>
              <a-checkbox
                v-model:checked="formData.containsTaxCalculation"
                style="margin-top: 10px">
                展示计税方式
              </a-checkbox>
            </div>
          </div>
          <div class="box-content">
            <div class="title">表眉设计</div>
            <div class="info">
              <div class="item">
                <span class="label">工程名称：</span>
                <a-select style="flex: 1" v-model:value="formData.projectName">
                  <a-select-option
                    v-for="item in projectNameList"
                    :key="item.key"
                    :value="item.label"
                    :disabled="item.disabled">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </div>
              <div class="item">
                <span class="label">页码展示：</span>
                <a-select style="flex: 1" v-model:value="formData.pageNumber">
                  <a-select-option
                    v-for="item in pageNumList"
                    :key="item.label"
                    :value="item.label"
                    :disabled="item.disabled">
                    {{ item.label }}
                  </a-select-option>
                </a-select>
              </div>
            </div>
          </div>
        </div>

        <div class="list-content" v-else>
          <div class="box-content">
            <div class="title">内容设计</div>
            <div class="info">
              <a-checkbox v-for="item in contentDesignList" v-model:checked="formData[item.key]">
                {{ item.label }}
              </a-checkbox>
            </div>
          </div>
          <div class="box-content">
            <div class="title">表头设计</div>
            <div class="info">
              <RichText
                v-model:defaultStyle="headLineStyle"
                @update:defaultStyle="headLineStyleChange"></RichText>
            </div>
          </div>
          <div class="box-content" style="margin-bottom: 0">
            <div class="title">列数据设计</div>
            <div class="column-info">
              <div class="column-tree">
                <a-tree
                  v-if="treeData.length"
                  v-model:selectedKeys="selectedKeys"
                  :tree-data="treeData"
                  :defaultExpandAll="true"
                  :fieldNames="{ title: 'name', key: 'id' }"
                  @select="treeSelect">
                  <template #switcherIcon="info"></template>
                </a-tree>
              </div>
              <div class="column-config">
                <div class="form-item">
                  <div class="label">列名</div>
                  <a-input
                    v-if="selectedItem"
                    style="flex: 1"
                    v-model:value="selectedItem.columnName"
                    @focus="columnFocus"
                    @blur="columnBlur"></a-input>
                </div>
                <div class="box-content">
                  <div class="title">正文格式</div>
                  <div class="info">
                    <RichText
                      v-model:defaultStyle="selectedItem.style"
                      @update:defaultStyle="mainTextStyleChange"></RichText>
                  </div>
                </div>
                <div class="form-item" style="justify-content: space-between">
                  <a-checkbox
                    @change="updateCellCustom"
                    v-model:checked="selectedItem.thousandSeparator">
                    千分符
                  </a-checkbox>
                  <div style="font-size: 14px; color: #2a2a2a">
                    小数位数
                    <a-input
                      @blur="updateCellCustom"
                      style="width: 70px"
                      v-if="selectedItem"
                      v-model:value="selectedItem.precision"></a-input>
                  </div>
                </div>
                <div class="form-item">
                  <a-checkbox
                    v-model:checked="selectedItem.isApplySameName"
                    @change="applySameChange">
                    应用至同名字段列
                  </a-checkbox>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="preview">
        <iframe
          v-if="fileUrl && previewData"
          id="myIframe"
          ref="iframeRef"
          :src="fileUrl"
          style="width: 100%; height: 100%; border: none" />
      </div>
    </div>
    <div class="btn-list">
      <a-button type="primary" @click="resetDefault">恢复默认设置</a-button>
      <a-button type="primary" @click="sheetSave">确定</a-button>
      <a-button @click="cancel">取消</a-button>
    </div>
  </common-modal>
</template>

<script setup>
import { ref, nextTick, toRaw, watch, reactive } from 'vue';
import infoMode from '@/plugins/infoMode.js';
import RichText from './RichText.vue';
import csProject from '@/api/csProject';
import {
  globalData,
  getLocalStorage,
  removeLocalStorage,
  setLocalStorage,
} from './../reportFrom.js';
import XEUtils from 'xe-utils';
import SheetHold from './SheetHold';
const emits = defineEmits(['refresh']);
let treeData = ref([]);
let selectedKeys = ref([]);
let selectedItem = ref(null);
const treeSelect = (selectedKeys, e) => {
  console.log('🚀 ~ treeSelect ~ selectedKeys, e:', selectedKeys, e);
  selectedItem.value = e.node.dataRef;
};
const columnFocus = () => {
  if (selectedItem.value.columnName) {
    selectedItem.value.prevContent = selectedItem.value.columnName; // 上次修改值，在和并列中做对比
  }
};
const columnBlur = () => {
  const { name, columnName, position } = selectedItem.value;
  if (!columnName) return;
  for (let item of treeData.value) {
    item.children = item.children?.map(t => {
      if (t.name === name) {
        t.columnName = columnName;
      }
      return t;
    });
  }
  const prevContent = selectedItem.value?.prevContent;
  SheetHoldClass.updateHeadLineContent(position, columnName, prevContent);
  console.log('🚀 ~ columnBlur ~ selectedItem.value:', selectedItem.value);
  pdfPreview();
};

const mainTextStyleChange = (val = null) => {
  console.log('🚀 ~ mainTextStyleChange ~ val:', val);
  let { rowList, position, isApplySameName, style } = selectedItem.value;
  let allRowList = [];
  if (isApplySameName) {
    for (let item of treeData.value) {
      allRowList = [...allRowList, ...item.rowList.map(t => (t = t - 1))];
    }
  } else {
    allRowList = rowList;
  }
  SheetHoldClass.updateCellStyleByRowColumnIndex(allRowList, position, val || style);
  pdfPreview();
};

const updateCellCustom = () => {
  let { thousandSeparator, precision, rowList, position, isApplySameName } = selectedItem.value;
  let allRowList = [];
  if (isApplySameName) {
    for (let item of treeData.value) {
      allRowList = [...allRowList, ...item.rowList.map(t => (t = t - 1))];
    }
  } else {
    allRowList = rowList;
  }
  precision = precision?.replace(/[^0-9]/g, '');
  SheetHoldClass.updateCellCustomByRCIndex(allRowList, position, { precision, thousandSeparator });
  pdfPreview();
};

const applySameChange = () => {
  let { isApplySameName } = selectedItem.value;
  if (isApplySameName) {
    updateCellCustom();
    mainTextStyleChange();
  }
};
const asideList = ref([
  {
    label: '页面设计',
    value: 'pageConfig',
  },
  {
    label: '报表内容',
    value: 'reportContent',
  },
]);
let asideAction = ref('pageConfig');
const asideClick = e => {
  console.log('🚀 ~ asideClick ~ e:', e);
  const value = e.target.dataset.value;
  if (value) asideAction.value = value;
};

const projectNameList = ref([
  {
    label: '`工程名称:`+{单项名称}+{单位名称}',
    key: 'unit',
  },
  {
    label: '`工程名称:`+{单项名称}',
    key: 'single',
  },
  {
    label: '`工程名称:`+{项目名称}',
    key: 'project',
  },
]);
const pageNumList = ref([
  {
    label: '`第`+{页码}+`页`+`共`+{总页数}+`页`',
  },
]);
let formData = reactive({
  landSpace: true,
  numberZero: false,
  emptyField: false,
  containsTaxCalculation: false,
  projectName: '`工程名称:`+{单项名称}+{单位名称}',
  pageNumber: '`第`+{页码}+`页`+`共`+{总页数}+`页`',
});
const projectNameListDefaultHandler = () => {
  const itemLevel = copyGlobalData?.treeParams.itemLevel;
  projectNameList.value.forEach(item => {
    item.disabled = true;
    if (itemLevel === 'project' && item.key === itemLevel) {
      item.disabled = false;
    }
    if (itemLevel === 'single' && ['project', 'single'].includes(item.key)) {
      item.disabled = false;
    }
    if (itemLevel === 'unit') {
      item.disabled = false;
    }
  });
  formData.projectName = projectNameList.value.find(item => item.key === itemLevel)?.label;
};

watch(
  () => XEUtils.clone(formData, true),
  (val, old) => {
    console.log('🚀 ~ watch ~ val:', val, old);
    if (val.projectName !== old.projectName || val.pageNumber !== old.pageNumber) {
      SheetHoldClass.updateSheetEyeBrowInfo(val.projectName, val.pageNumber);
      copyGlobalData.excelDataTemplate = SheetHoldClass.jsonData;
    }
    pdfPreview();
  },
  {
    deep: true,
  }
);

const contentDesignList = ref([
  { label: '数值0输出为空', key: 'numberZero', value: 1 },
  { label: '空字段输出为“-”', key: 'emptyField', value: 2 },
]);
let dialogVisible = ref(false);

const cancel = () => {
  dialogVisible.value = false;
};

const fileUrl = ref();
const previewData = ref(null);
const storageName = ref('rapidDesignReportForm');
let lanMuName = ref('');
let copyGlobalData = null;
const initFormDate = () => {
  const { emptyField, numberZero, print } = getLocalStorage();
  console.log('🚀 ~ initFormDate ~ getLocalStorage():', getLocalStorage());
  formData.containsTaxCalculation = copyGlobalData.containsTaxCalculation;
  formData.landSpace = print?.landSpace;
  formData.numberZero = false;
  formData.emptyField = false;
  if (typeof numberZero === 'boolean') {
    formData.numberZero = numberZero;
  }
  if (typeof emptyField === 'boolean') {
    formData.emptyField = emptyField;
  }
  const name = SheetHoldClass.getCellDataAttrByFiled();
  if (name) {
    formData.projectName = name;
  }
};
let headTitle = ref('');
let headerStyle = ref(null);

let headLineStyle = ref(null);
let SheetHoldClass = null;
watch(
  () => headerStyle.value,
  val => {
    if (!val) return;
    SheetHoldClass.updateHeaderStyle(val);
    pdfPreview();
  },
  { deep: true, immediate: true }
);
const headLineStyleChange = val => {
  SheetHoldClass.updateHeadLineStyle(val);
  pdfPreview();
};

const open = info => {
  fileUrl.value = null;
  asideAction.value = 'pageConfig';
  lanMuName.value = info.lanMuName;
  copyGlobalData = XEUtils.clone(globalData, true);
  console.log('🚀 ~ copyGlobalData:', copyGlobalData, info);
  SheetHoldClass = new SheetHold(copyGlobalData?.excelDataTemplate);

  const headArr = copyGlobalData.headLine.split(' ');
  headTitle.value = headArr[headArr.length - 1];
  headerStyle.value = SheetHoldClass.getStyleByFiled();
  headLineStyle.value = SheetHoldClass.getStyleByFiled('headLine');
  projectNameListDefaultHandler();
  initFormDate();
  queryJsonDataColumn();
  dialogVisible.value = true;
  pdfPreview();
};
const pdfPreview = async () => {
  copyGlobalData.excelDataTemplate = SheetHoldClass.jsonData;
  const data = await getShowSheetStyle(copyGlobalData);
  // fileUrl.value = null;
  previewData.value = data;
  data ? setLocalStorage(data, storageName.value) : removeLocalStorage(storageName.value);
  resetPreview();
};
const resetPreview = () => {
  nextTick(() => {
    const zoom = getZoom();
    fileUrl.value = `/pdf/index.html?storageName=${storageName.value}&zoom=${zoom}&temp=${new Date().getTime()}`;
  });
};

let iframeRef = ref();
const getZoom = () => {
  const reportFormWidth = formData.landSpace ? 1087.5 : 770;
  const iframeWidth = document.querySelector('.preview').clientWidth;
  const zoom = (iframeWidth / reportFormWidth).toFixed(4);
  return zoom;
};

const queryJsonDataColumn = () => {
  const params = {
    ...toRaw(copyGlobalData.treeParams.constructObj),
    itemLevel: copyGlobalData.treeParams.itemLevel,
    lanMuName: lanMuName.value,
    headLine: copyGlobalData.headLine,
  };
  console.log('🚀 ~ queryJsonDataColumn ~ params:', params);
  csProject.queryJsonDataColumn(params).then(res => {
    console.log('🚀 ~ queryJsonDataColumn ~ res:', res);
    if (res.code === 200) {
      treeData.value = (res.result || []).map(item => {
        item.selectable = false;
        // item.disabled = true;
        item.children = item.children?.map(t => {
          t.rowList = item.rowList.map(n => (n = n - 1));
          t.columnName = t.name;
          t.prevContent = t.name;
          t.thousandSeparator = !!t.thousandSeparator;
          t.position = t.position.map(n => (n = n - 1));
          t.precision = t.precision;
          t.isApplySameName = true;
          t.style = SheetHoldClass.getCellStyleByRowColumnIndex(t.rowList[0], t.position[0]) || {};
          return t;
        });
        return item;
      });
      const firstItem = treeData.value[0]?.children[0];
      if (firstItem) {
        selectedKeys.value = [firstItem.id];
        selectedItem.value = firstItem;
        console.log('🚀 ~ queryJsonDataColumn ~ selectedItem.value:', selectedItem.value);
      }
    }
  });
};
// 获取表单数据
const getShowSheetStyle = sheetInfo => {
  return new Promise((resolve, reject) => {
    const postData = {
      ...toRaw(copyGlobalData.treeParams.constructObj),
      itemLevel: copyGlobalData.treeParams.itemLevel,
      lanMuName: lanMuName.value,
      headLine: sheetInfo.headLine,
      isLandScape: formData.landSpace,
      jsonData: toRaw(copyGlobalData?.excelDataTemplate),
      containsTaxCalculation: formData.containsTaxCalculation,
      numberZero: formData.numberZero,
      emptyField: formData.emptyField,
    };
    console.log('🚀 ~ returnnewPromise ~ postData:', postData);
    csProject.displayQuickDesign(postData).then(res => {
      if (res.code == 200) {
        resolve(res.result);
      }
    });
  });
};
const sheetSave = () => {
  SheetHoldClass.clearAbandonedStyleId();
  const postData = {
    ...toRaw(copyGlobalData.treeParams.constructObj),
    itemLevel: copyGlobalData.treeParams.itemLevel,
    lanMuName: lanMuName.value,
    headLine: copyGlobalData.headLine,
    isLandScape: formData.landSpace,
    jsonData: toRaw(copyGlobalData?.excelDataTemplate),
    containsTaxCalculation: formData.containsTaxCalculation,
    numberZero: formData.numberZero,
    emptyField: formData.emptyField,
  };
  console.log('🚀 ~ sheetSave ~ postData:', postData);
  csProject.confirmDisplayQuickDesign(postData).then(res => {
    console.log('🚀 ~ csProject.confirmDisplayQuickDesign ~ res:', res, postData);

    if (res.code == 200) {
      cancel();
      emits('refresh');
    }
  });
};
const resetDefault = () => {
  infoMode.show({
    iconType: 'icon-querenshanchu',
    infoText: '是否恢复当前报表默认设置',
    confirm: () => {
      restoreDesign(infoMode.hide);
    },
    close: () => {
      infoMode.hide();
    },
  });
};
const restoreDesign = callback => {
  const postData = {
    ...toRaw(copyGlobalData.treeParams.constructObj),
    itemLevel: copyGlobalData.treeParams.itemLevel,
    lanMuName: lanMuName.value,
    headLine: copyGlobalData.headLine,
  };
  console.log('🚀 ~ postData:', postData);
  csProject.restoreOriginDesign(postData).then(res => {
    console.log(res);
    if (typeof callback === 'function') callback();
    emits('refresh');
    cancel();
  });
};
defineExpose({ open });
</script>
<style lang="scss" scoped>
.column-info {
  display: flex;
  justify-content: space-between;
  .column-tree {
    width: 155px;
    padding: 10px;
    max-height: 250px;
    overflow-y: auto;
    border: 1px solid #d9d9d9;
  }
  .column-config {
    flex: 1;
    padding-left: 10px;
    .box-content {
      padding: 10px 7px 5px;
      margin-bottom: 8px;
      margin-top: 18px;
    }
    .form-item {
      margin-bottom: 5px;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      .label {
        font-size: 14px;
        padding-right: 10px;
      }
    }
  }
}
.btn-list {
  display: flex;
  justify-content: center;
  margin-top: 12px;
  :deep(.ant-btn) {
    margin: 0 5px;
  }
}
.rapid-design-content {
  display: flex;
  border: 1px solid #e1e1e1;
  height: calc(100% - 35px);
  min-height: 500px;
}
.box-content {
  position: relative;
  border: 1px solid #d9d9d9;
  padding: 17px 12px 13px;
  margin-bottom: 25px;
  .title {
    position: absolute;
    top: -11px;
    left: 12px;
    font-size: 14px;
    line-height: 1.5;
    padding: 0 3px;
    background: #fff;
    color: #2a2a2a;
  }
  .item {
    display: flex;
    align-items: center;
    &:last-child {
      margin-top: 10px;
    }
  }
}
.aside {
  width: 17.3%;
  border-right: 1px solid #e1e1e1;
  text-align: center;
  .list {
    font-size: 14px;
    line-height: 32px;
    color: #2a2a2a;
    cursor: pointer;
    &.active {
      color: #fff;
      background: #287cfa;
    }
  }
}
.content {
  width: 51%;
  border-right: 1px solid #e1e1e1;
  height: 100%;
  padding: 20px 10px;
  :deep(.ant-radio-wrapper) {
    margin-right: 90px;
  }
}
.preview {
  width: 31.7%;
  height: 100%;
}
</style>
