const {ResponseData} = require("../utils/ResponseData");
const {Controller} = require("../../core");
class OtherProjectZgjController extends Controller{


    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
    }

    /**
     * 专业工程暂估价 操作
     * @param arg 清单册code
     * @returns {Promise<*>}
     */
    async otherProjectZygcZgj(arg,redo="专业工程暂估价 {columnTitle} 由【{oldValue}】修改为【{newValue}】") {

        const result = await this.service.otherProjectZgjService.otherProjectZygcZgj(arg);

        if (arg.operateType !==1){

            await this.service.management.sycnTrigger("unitDeChange");
            await this.service.management.trigger("itemChange");
        }
        return ResponseData.success(result);
    }

}

OtherProjectZgjController.toString = () => '[class OtherProjectZgjController]';
module.exports = OtherProjectZgjController;