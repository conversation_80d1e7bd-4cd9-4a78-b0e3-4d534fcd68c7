const {Controller} = require("../../core");
const Log = require("../../core/log");
const {ResponseData} = require("../utils/ResponseData");

/**
 * 清单指引 controller
 */
class QdGuideController extends Controller {

    /**
     * 构造函数
     * @param ctx
     */
    constructor(ctx) {
        super(ctx);
        this._qdGuideProcess = this.service.qdGuideProcess;
        this._baseListService = this.service.baseListService;
    }

    /**
     * 指引库列表
     * @param params constructId工程项目id
     */
    listGuideLibrary(params) {
        const result = this._qdGuideProcess.listGuideLibrary(params);
        return ResponseData.success(result);
    }

    /**
     * 获取清单分类目录树- 4层树：bd_name_level01、bd_name_level02、bd_name_level03、bd_code_level04 bd_name_level04
     * @param params 清单code或name 模糊查询; libraryCode 清单册code
     */
    async qdLevelTree(params) {
        // let {qdCodeOrName, libraryCode} = params;
        const result = await this._baseListService.listQdTree(params);
        return ResponseData.success(result);
    }

    /**
     * 清单指引定额列表
     * @param params guideLibraryModel 清单指引库model, baseListModel base清单model
     */
    async qdGuideDeList(params) {
        //let {guideLibraryModel, baseListModel} = params;
        const result = await this._qdGuideProcess.listQdGuideDeArray(params);
        return ResponseData.success(result);
    }

    /**
     * 插入子目（即插入定额）
     * @param params 在原插入定额接口入参的基础上 增加deArray：定额数组
     */
    async saveDeArray(params, redo='插入子目') {
        // 在原定额索引插入接口入参的基础上增加 deArray定额数组
        // let {deArray, ...} = params;
        const result = await this._qdGuideProcess.saveDeArray(params);
        return ResponseData.success(result);
    }

    /**
     * 插入清单和定额Array
     * @param params 在原插入清单接口入参的基础上 增加baseListModel:base清单, deArray：定额数组
     */
    async saveQdAndDeArray(params,redo="插入{kind}") {
        // 在原清单索引插入接口入参的基础上增加 baseListModel，deArray定额数组
        // let {baseListModel, deArray, ...} = params;
        const result = await this._qdGuideProcess.saveQdAndDeArray(params);
        return ResponseData.success(result);
    }

    /**
     * 替换清单及插入定额
     * @param params 在原插入清单接口入参的基础上 增加pointLine选中行, baseListModel清单base，deArray定额数组
     */
    async replaceQdAndSaveDeArray(params,redo="替换{kind}") {
        // 在原清单索引替换接口入参的基础上增加 pointLine选中行, baseListModel清单base，deArray定额数组
        // let {pointLine, baseListModel, deArray, ...} = params;
        const result = await this._qdGuideProcess.replaceQdAndSaveDeArray(params);
        return ResponseData.success(result);
    }

    /**
     * 定位清单指引
     * @param params {standardId, libraryCode, constructId} 国标清单id，清单册code
     */
    async locateQdGuide(params) {
        let {standardId, libraryCode, constructId,singleId,unitId} = params;
        const result = await this._qdGuideProcess.locateQdGuide(standardId, libraryCode, constructId,singleId,unitId);
        return  ResponseData.success(result);
    }

}

QdGuideController.toString = () => '[class QdGuideController]';
module.exports = QdGuideController;