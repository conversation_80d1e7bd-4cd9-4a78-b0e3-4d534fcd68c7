const {Controller} = require("../../../core");
const {ResponseData} = require("../utils/ResponseData");
const ProjectDomain = require("../domains/ProjectDomain");
const WildcardMap = require("../core/container/WildcardMap");
const {ObjectUtils} = require("../utils/ObjectUtils");
const CommonConstants = require("../constants/CommonConstants");
const gsRcjCollectScType = require('../jsonData/glj_rcj_collect_sc_type.json');
const {ConvertUtil} = require("../utils/ConvertUtils");


class GljRcjCollectController extends Controller {

  /**
   * 构造函数
   * @param ctx
   */
  constructor(ctx) {
    super(ctx);
  }

  /**
   * 获取rcj汇总菜单
   * @param arg
   * @return {Promise<void>}
   */
  async getRcjCellectMenuData(arg){
    let menuData =await  this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectMenuData(arg);
    return ResponseData.success(menuData);
  }


  /**
   * 获取rcj分类设置
   * @param arg
   * @returns {Promise<ResponseData>}
   */
  async getRcjCellectTypeData(arg){
    let typeData =await  this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectTypeData(arg);
    return typeData;
  }



  /**
   *  新增、修改rcj汇总菜单
   * @param arg
   * @return {Promise<void>}
   */
  async saveRcjCellectMenuData(arg, redo="插入或编辑- -汇总分类"){
    let result =await  this.service.gongLiaoJiProject.gljRcjCollectService.saveRcjCellectMenuData(arg);
    return result;
  }


  /**
   *  删除rcj汇总菜单
   * @param arg
   * @return {Promise<void>}
   */
  async delRcjCellectMenuData(arg, redo="删除- -汇总分类"){
    let result =await  this.service.gongLiaoJiProject.gljRcjCollectService.delRcjCellectMenuData(arg);
    return result;
  }

  /**
   *  菜单各个类型顺序保存
   * @param arg
   * @return {Promise<void>}
   */
  async saveRcjCellectMenuSort(arg){
    let result =await  this.service.gongLiaoJiProject.gljRcjCollectService.saveRcjCellectMenuSort(arg);
    return result;
  }

  /**
   *  菜单各个类型顺序保存(公共版本)
   * @param arg
   * @return {Promise<void>}
   */
  async saveRcjCellectMenuSortShare(arg, redo="移动- -人材机"){
    let result =await  this.service.gongLiaoJiProject.gljRcjCollectService.saveRcjCellectMenuSortShare(arg);
    return result;
  }

  /**
   * 获取rcj汇总
   * @param arg
   * @return {Promise<void>}
   */
  async getRcjCellectData(arg){
    let result =await  this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectData(arg);
    // 其他材料费QTCLF1单位为元,特殊人材机（费用人材机）单位为元(仅前端使用)
    if (ObjectUtils.isNotEmpty(result)) {
      result = ConvertUtil.deepCopy(result);
      result.forEach(item=>{
        if (item.materialCode.includes('QTCLF1')) {
          item.unit = '元'
        }

        if (item.isFyrcj == 0) {
          item.unit = '元'
        }

        this.service.gongLiaoJiProject.gljBaseRcjService.typeListByKind(item);

      })
    }



    return  ResponseData.success(result);
  }


  /**
   * 暂估人材机
   * @param arg
   * @returns {Promise<ResponseData>}
   */
  async getZgRcjCellectData(arg) {
    let result = await this.getRcjCellectData(arg);
    let newResult = result.result.filter(item => item.ifProvisionalEstimate === 1);
    return ResponseData.success(newResult);
  }


  /**
   *  存价内容查询
   * @param arg
   * @return {Promise<void>}
   */
  async getStockPrice(args){
    let result =await  this.service.gongLiaoJiProject.gljRcjCollectService.getStockPrice(args);
    return  ResponseData.success(result);
  }

  /**
   * 获取rcj 局部汇总
   * @param arg
   * @return {Promise<void>}
   */
  async getPartRcjCellectData(arg){
    // let result = await this.service.gongLiaoJiProject.gljRcjCollectService.getPartRcjCellectData(arg);
    let jbhzInfo = await this.service.gongLiaoJiProject.gljUnitCostSummaryService.getPartCostSummaryInfo(arg.constructId);
    let {jbhzConstructId, jbhzFilePath} = jbhzInfo;
    let param = {
      constructId: jbhzConstructId,
      singleId: arg.singleId,
      unitId: arg.unitId,
      levelType: 3,
      kind: 0,
    }
    let result = await this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectData(param);
    // 其他材料费QTCLF1单位为元,特殊人材机（费用人材机）单位为元(仅前端使用)
    if (ObjectUtils.isNotEmpty(result)) {
      result = ConvertUtil.deepCopy(result);
      result.forEach(item=>{
        if (item.materialCode.includes('QTCLF1')) {
          item.unit = '元'
        }

        if (item.isFyrcj == 0) {
          item.unit = '元'
        }

      })
    }else {
      result = [];
    }
    return  ResponseData.success(result);
  }

  /**
   * 导出rcj 局部汇总
   * @param arg
   * @return {Promise<void>}
   */
  async exportPartRcjCellectData(arg){
    let result = await this.service.gongLiaoJiProject.gljRcjCollectService.exportPartRcjCellectData(arg);
    return  ResponseData.success(result);
  }




  /**
   * 验证能否创建分类汇总
   * @param arg
   * @return {Promise<void>}
   */
  async verifyRcjCellectMenu(arg){
    let result =await  this.service.gongLiaoJiProject.gljRcjCollectService.verifyRcjCellectMenu(arg);
    return  ResponseData.success(result);
  }


  /**
   * 增加rcj汇总颜色
   * @param arg
   * @return {Promise<void>}
   */
  async saveRcjCellectColor(args, redo="标记- -人材机汇总数据颜色"){
    let result =await  this.service.gongLiaoJiProject.gljRcjCollectService.saveRcjCellectColor(args);
    return result;
  }

  /**
   * rcj汇总颜色删除
   * @param arg
   * @return {Promise<void>}
   */
  async delRcjCellectColor(args){
    let result =await  this.service.gongLiaoJiProject.gljRcjCollectService.delRcjCellectColor(args);
    return result;
  }

  /**
   *  人材机汇总修改rcj
   * @param arg
   * @return {Promise<void>}
   */
  async updateRcjCellect(args, redo="编辑或统一应用- -人材机汇总数据"){
    let result =await  this.service.gongLiaoJiProject.gljRcjCollectService.updateRcjCellect(args);
    return result;
  }

  /**
   *  批量删除批注
   * @param arg
   * @return {Promise<void>}
   */
  async batchDeleteAnnotations(args,redo="删除- -人材机所有批注"){
    let result =await  this.service.gongLiaoJiProject.gljRcjCollectService.batchDeleteAnnotations(args);
    return result;
  }

  /**
   *  取消排序
   * @param arg
   * @return {Promise<void>}
   */
  async removeRcjCellectSort(args, redo="取消- -人材机汇总数据排序"){
    let result =await  this.service.gongLiaoJiProject.gljRcjCollectService.removeRcjCellectSort(args);
    return result;
  }


  /**
   *  是否排序
   * @param arg
   * @return {Promise<void>}
   */
  async isRcjCellectSort(args){
    let result =await this.service.gongLiaoJiProject.gljRcjCollectService.isRcjCellectSort(args);
    return result;
  }

  /**
   *  人材机汇总无价差
   * @param arg
   * @return {Promise<void>}
   */
  async updateRcjCellectNoPriceDifference(args, redo="执行- -人材机无价差"){
    let result =await  this.service.gongLiaoJiProject.gljRcjCollectService.updateRcjCellectNoPriceDifference(args);
    return result;
  }


  /**
   *  人材机范围汇总
   * @param arg
   * @return {Promise<void>}
   */
  async getRCJProjectTreeMenu(args){
    let result =await  this.service.gongLiaoJiProject.gljRcjCollectService.getRCJProjectTreeMenu(args);
    return result;
  }

  /**
   *   修改汇总范围
   * @param args
   * @returns {Promise<*>}
   */
  async updateRCJProjectTreeMenu(args){
    let result =await  this.service.gongLiaoJiProject.gljRcjCollectService.updateRCJProjectTreeMenu(args);
    return result;
  }


  /**
   * 鼠标右键查询人材机关联定额树结构
   * @param args
   * @return {Promise<ResponseData>}
   */
  async getRelationTree(args){
    let result = await  this.service.gongLiaoJiProject.gljRcjCollectService.getRelationTree(args);
    return result;
  }


  /**
   * 鼠标右键 查询关联定额
   * @param args
   * @return {Promise<ResponseData>}
   */
  async getRelationDe(args){
    const result = await  this.service.gongLiaoJiProject.gljRcjCollectService.getRelationDe(args);
    return result;

  }

  /**
   * 人材机汇总——过滤——查询颜色
   * @param args
   * @returns {Promise<*>}
   */
  async getFiltrationColor(args){
    const result = await  this.service.gongLiaoJiProject.gljRcjCollectService.getFiltrationColor(args);
    return result;

  }

  /**
   * 工程项目汇总——临时数据保存
   * @param args
   * @returns {Promise<*|void>}
   */
  async temporaryDataSave(args){
    const result = await  this.service.gongLiaoJiProject.gljRcjCollectService.temporaryDataSave(args);
    return result;
  }


  /**
   * 工程项目汇总——临时数据查询
   * @param args
   * @returns {Promise<*>}
   */
  async temporaryDataGet(args){
    const result = await  this.service.gongLiaoJiProject.gljRcjCollectService.temporaryDataGet(args);
    return result;
  }


  /**
   * 工程项目汇总——临时数据删除
   * @param args
   * @returns {Promise<*>}
   */
  async temporaryDataDel(args){
    const result = await  this.service.gongLiaoJiProject.gljRcjCollectService.temporaryDataDel(args);
    return result;
  }



  /**
   *  单位工程——合并相似材料-查询
   * @param args
   * @returns {Promise<*>}
   */
  async getUnitMergeMaterials(args){
    const result = await  this.service.gongLiaoJiProject.gljRcjCollectService.getUnitMergeMaterials(args);
    return result;
  }


  /**
   * 工程项目——合并相似材料-查询
   * @param args
   * @returns {Promise<*>}
   */
  async getProjectMergeMaterials(args){
    const result = await  this.service.gongLiaoJiProject.gljRcjCollectService.getProjectMergeMaterials(args);
    return result;
  }


  /**
   *  合并相似材料-保存
   * @param args
   * @returns {Promise<*>}
   */
  async saveMergeMaterials(args, redo="合并- -人材机相似材料"){
    return await this.service.gongLiaoJiProject.gljRcjCollectService.saveMergeMaterials(args);
  }


  /**
   *  单位人材机汇总-市场价系数调整
   * @param args
   * @returns {Promise<*>}
   */
  async marketPriceAdjustUnit(args, clearRedo="清除历史记录- -调整人材机市场价系数") {
    const result = await this.service.gongLiaoJiProject.gljRcjCollectService.marketPriceAdjustUnit(args);
    return result;
  }

  /**
   *  项目人材机汇总-市场价系数调整&应用
   * @param args
   * @returns {Promise<*>}
   */
  async marketPriceAdjustConstructApply(args, redo="应用调整- -人材机市场价系数") {
    const result = await this.service.gongLiaoJiProject.gljRcjCollectService.marketPriceAdjustConstructApply(args);
    return result;
  }


  /**
   *  单位人材机汇总-三材种类查询
   * @param args
   * @returns {Promise<*>}
   */
  async getKindSc(args) {
    return ResponseData.success(gsRcjCollectScType) ;
  }

  /**
   *  项目人材机汇总-三材列表查询
   * @param args
   * @returns {Promise<*>}
   */
  async getScList(args) {
    const result = await this.service.gongLiaoJiProject.gljRcjCollectService.getScList(args);
    return ResponseData.success(result) ;
  }


  /**
   *   获取直接分摊费
   * @param args
   * @returns {Promise<*>}
   */
  async getShareCost(args) {
    const result = await this.service.gongLiaoJiProject.gljRcjCollectService.getShareCost(args);
    return ResponseData.success(result) ;
  }


  /**
   *  保存直接分摊费
   * @param args
   * @returns {Promise<*>}
   */
  async saveShareCost(args, redo="编辑- -人材机分摊") {
    const result = await this.service.gongLiaoJiProject.gljRcjCollectService.saveShareCost(args);
    return ResponseData.success(result) ;
  }

  /**
   *  清理直接分摊费
   * @param args
   * @returns {Promise<*>}
   */
  async dropShareCost(args, redo="清除- -人材机分摊") {
    const result = await this.service.gongLiaoJiProject.gljRcjCollectService.dropShareCost(args);
    return ResponseData.success(result) ;
  }

  /**
   *  人材机汇总替换
   * @param args
   * @returns {Promise<*>}
   */
  async updateRcjByCollect(args, redo="替换- -人材机汇总数据") {
    const result = await this.service.gongLiaoJiProject.gljRcjCollectService.updateRcjByCollect(args);
    return ResponseData.success(result) ;
  }


  /**
   * 获取单位工程设置主要材料
   */
  async getUnitMainMaterialSetting(args) {
    let res = await this.service.gongLiaoJiProject.gljRcjCollectService.getUnitMainMaterialSetting(args);
    return ResponseData.success(res);
  }

  /**
   * 修改单位工程设置主要材料
   */
  async updateUnitMainMaterialSetting(args, redo="设置- -单位工程主要材料") {
    let res = await this.service.gongLiaoJiProject.gljRcjCollectService.updateUnitMainMaterialSetting(args);
    return ResponseData.success(res);
  }

  /**
   * 来源分析 查询
   * @param arg
   * @returns {Promise<void>}
   */
  async getRcjCellectAnalyseData(arg){
    let result =await  this.service.gongLiaoJiProject.gljRcjCollectService.getRcjCellectAnalyseData(arg);
    return  ResponseData.success(result);
  }

  // /**
  //  * 获取暂估人材机
  //  * @param args
  //  * @returns {*}
  //  */
  // async getZgRcjCellect(args) {
  //   return ResponseData.success(await this.service.gongLiaoJiProject.gljRcjCollectService.getZgRcjCellect(args));
  // }
  //
  // /**
  //  * 上移下移暂估人材机
  //  * @param args
  //  * @returns {*}
  //  */
  // async moveUpAndDownZgRcjCellect(args) {
  //   return await this.service.gongLiaoJiProject.gljRcjCollectService.moveUpAndDownZgRcjCellect(args);
  // }


  /**
   * 单位工程人材机汇总 下拉修改
   * @param args
   * @returns {Promise<ResponseData>}
   */
  async unitRcjBatchUpdate(args) {
    const result = await this.service.gongLiaoJiProject.gljRcjCollectService.unitRcjBatchUpdate(args);
    return ResponseData.success(result);
  }


  /**
   * 新插入人材机处理
   * @param arg
   * @returns {Promise<void>}
   */
  async rcjAndMemoryHandle(arg){
    let result =await  this.service.gongLiaoJiProject.gljRcjCollectService.rcjAndMemoryHandle(arg.constructId,arg.unitId,arg.rcj);
    return  ResponseData.success(result);
  }


}
GljRcjCollectController.toString = () => '[class GsRcjCollectController]';
module.exports = GljRcjCollectController;
