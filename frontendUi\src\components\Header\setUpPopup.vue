<template>
  <commonModal
    className="dialog-comm page-column-setting"
    v-model:modelValue="visible"
    title="设置"
    @show="openModal"
    @close="close">
    <div class="page-setting">
      <div class="tree-box">
        <a-tree
          :tree-data="treeData"
          :defaultExpandAll="true"
          v-if="treeData.length"
          v-model:selectedKeys="tabKey"
          @select="treeSelect"
          :field-names="{
            children: 'children',
            title: 'name',
          }"></a-tree>
      </div>
      <div class="setting-box">
        <div class="content-box">
          <div class="list-content">
            <div
              :class="{
                'list-box': true,
                'list-group': options.constructor === Object,
              }"
              v-for="(options, index) of currentTreeInfo">
              <template v-if="options.constructor === Object">
                <div class="title">
                  <span v-if="!options.tooltip">{{ options.title }}</span>
                  <span v-else>
                    <a-tooltip placement="rightTop" color="white">
                      <template #title>
                        <div style="color: #000000; font-size: 12px">
                          <p style="margin: 0">
                            {{ options.toolInfo }}
                          </p>
                        </div>
                      </template>
                      {{ options.title }}
                      <icon-font type="icon-bangzhu" style="margin: 0 5px"></icon-font>
                    </a-tooltip>
                  </span>
                </div>
                <setItem :options="options.options"></setItem>
              </template>
              <template v-if="Array.isArray(options)">
                <setItem :options="options" @onChange="onChange"></setItem>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="page-setting-foot">
      <div class="right">
        <a-button style="margin-right: 25px" @click="close">取消</a-button>
        <a-button type="primary" @click="save">确定</a-button>
      </div>
    </div>
  </commonModal>
</template>
<script setup>
import { projectDetailStore } from '@/store/projectDetail';
import { message } from 'ant-design-vue';
import { ref } from 'vue';

const projectStore = projectDetailStore();
const emits = defineEmits(['closePopup']);

let treeData = ref([
  {
    key: 'system',
    name: '软件级别',
    children: [
      {
        key: 'shuRu',
        name: '输入设置',
        options: [
          [
            {
              name: '复制子目数据时，子目消耗量不变',
              field: 'fzzmsjxhlbb',
              type: 'checkbox',
              value: true,
              disabled: true,
            },
            {
              name: '直接输入标准子目时，弹出标准换算弹窗',
              field: 'zjsrbzzmtcbzhtk',
              type: 'checkbox',
              value: true,
              disabled: true,
            },
            {
              name: '通过修改子目名称完成砼/浆替换',
              field: 'xgzmmcwcgjth',
              type: 'checkbox',
              value: true,
              disabled: true,
            },
            {
              name: '直接输入编码时，复用当前单位工程中已有的清单或子目',
              field: 'zjsrbmfydqyyqdzm',
              type: 'checkbox',
              value: true,
              disabled: true,
            },
            {
              name: '输入名称，关联查询当前定额册中的子目或清单',
              field: 'srmcglcxdqdeczmqd',
              type: 'checkbox',
              value: true,
              disabled: true,
            },
            {
              name: '输入清单工程量回车跳到子目行',
              field: 'qdgclhctdzmh',
              type: 'checkbox',
              value: true,
              disabled: true,
            },
            {
              name: '输入清单后自动带出工作内容行',
              field: 'srqdhzddcgznrh',
              type: 'checkbox',
              value: false,
              disabled: true,
            },
            {
              name: '子目工程量自动等于清单工程量',
              field: 'zmgclzddyqdgcl',
              type: 'checkbox',
              value: true,
              disabled: true,
            },
            {
              name: '复制清单时，保留原清单编码不变',
              field: 'fzqdblyqdbmbb',
              type: 'checkbox',
              value: true,
              disabled: true,
            },
            {
              name: '定额工程量联动清单',
              field: 'degclldqd',
              type: 'checkbox',
              value: true,
              disabled: true,
            },
          ],
        ],
      },
      {
        key: 'file',
        name: '文件管理',
        options: [
          [
            {
              name: '文件定额存储时间设置(分钟)',
              field: 'wjdeccsjsz',
              type: 'input',
              value: '5',
              disabled: false,
            },
          ],
          [
            {
              name: '文件默认保存路径设置',
              field: 'DEF_SAVE_PATH',
              type: 'filePath',
              value: '',
              disabled: false,
            },
          ],
          [
            {
              name: '文件下载路径',
              field: 'FILE_DOWNLOAD_PATH',
              type: 'filePath',
              value: '',
            },
          ],
        ],
      },
    ],
  },
  {
    key: 'yuSuanShu',
    name: '预算书设置',
    children: [
      // {
      //   key: 'setting',
      //   name: '便捷性设置',
      //   options: [
      //     [
      //       {
      //         name: '推荐项目特征选中后自动关联组价方案',
      //         field: 'settingValue',
      //         type: 'checkbox',
      //         value: false,
      //       },
      //       // {
      //       //   name: '以系数计算的措施项目和机械台班中的人工单价参与调整',
      //       //   field: 'rgfInMeasureAndRPriceInMechanicalAction',
      //       //   field: 'yxsjsdcsxmhjxtbzdrgdjcytz',
      //       //   type: 'checkbox',
      //       //   value: false,
      //       // },
      //       {
      //         name: '单位工程中标准换算弹窗展示',
      //         field: 'standardConversionShowFlag',
      //         type: 'checkbox',
      //         value: false,
      //       },
      //       {
      //         name: '单位工程中主材弹窗展示',
      //         field: 'mainRcjShowFlag',
      //         type: 'checkbox',
      //         value: false,
      //       },
      //       {
      //         name: '展示定额关联子目弹窗',
      //         field: 'deGlTcFlag',
      //         type: 'checkbox',
      //         value: false,
      //       },
      //       {
      //         name: '锁定人材机消耗量',
      //         field: 'lockRcjResQty',
      //         type: 'checkbox',
      //         value: false,
      //       },
      //     ],
      //   ],
      // },

      {
        key: 'jiSuan',
        name: '计算设置',
        options: [
          [
            {
              name: '工程量单位',
              field: 'gcldw',
              type: 'radio',
              value: '2',
              disabled: false,
              options: [
                {
                  label: '自然单位',
                  value: '1',
                },
                {
                  label: '定额单位',
                  value: '2',
                },
              ],
            },
          ],
          [
            {
              name: '主材、设备计取价差',
              field: 'zccjDesc',
              type: 'checkbox',
              value: true,
              disabled: false,
            },
            {
              name: '补充人材机计取差价',
              field: 'bccjDesc',
              type: 'checkbox',
              value: true,
              disabled: false,
            },
            {
              name: '暂估人材机计取差价',
              field: 'zgcj',
              type: 'checkbox',
              value: true,
              disabled: true,
            },
            {
              name: '修改市场价同步整个单位工程',
              field: 'xgscjtb',
              type: 'checkbox',
              value: true,
              disabled: true,
            },
          ],
          [
            {
              name: '清单综合合价计算方式',
              field: 'qdzhjsfs',
              type: 'radio',
              value: '1',
              disabled: false,
              options: [
                {
                  label: '清单综合合价=清单综合单价*清单工程量',
                  value: '1',
                },
                {
                  label: '清单综合合价=∑子目综合合价',
                  value: '2',
                },
              ],
            },
          ],
          [
            {
              name: '综合单价计算方式',
              field: 'zhdjjsfs',
              type: 'radio',
              value: '2',
              options: [
                {
                  label: '清单单价取费（清单综合单价=人工费+材料费+机械费+管理费+利润）',
                  value: '1',
                },
                {
                  label: '子目单价取费（清单综合单价=∑子目综合合价/清单工程量）',
                  value: '2',
                },
              ],
            },
          ],
          [
            {
              name: '暂估合价计算方式',
              field: 'zfhjjsfs',
              type: 'radio',
              value: '2',
              disabled: true,
              options: [
                {
                  label: '暂估合价=暂估单价*工程量（清单、子目）',
                  value: '1',
                },
                {
                  label: '暂估合价=∑暂估材料数量*暂估材料单价',
                  value: '2',
                },
              ],
            },
          ],
          // 58-ceping分支设置22数据配合比取消二次分析--全局设直接隐藏此部分
          // {
          //   title: '配比、机械台班设置',
          //   options: [
          //     {
          //       name: '商砼浆支持二次解析',
          //       field: 'stjecjx',
          //       type: 'checkbox',
          //       value: false,
          //       disabled: true,
          //     },
          //     {
          //       name: '现浇砼浆支持二次解析',
          //       field: 'xjtjecjx',
          //       type: 'checkbox',
          //       value: true,
          //       disabled: true,
          //     },
          //     {
          //       name: '现浇砼浆可直接修改市场价',
          //       field: 'xjtjxgscj',
          //       type: 'checkbox',
          //       value: false,
          //       disabled: true,
          //     },
          //     {
          //       name: '机械台班支持二次解析',
          //       field: 'jxtbecjx',
          //       type: 'checkbox',
          //       value: true,
          //       disabled: true,
          //     },
          //     {
          //       name: '机械台班可直接修改市场价',
          //       field: 'jxtbxgscj',
          //       type: 'checkbox',
          //       value: false,
          //       disabled: true,
          //     },
          //   ],
          // },
          [
            {
              name: '人材机消耗量',
              field: 'rcjResQty',
              type: 'radio',
              value: '1',
              disabled: true,
              options: [
                {
                  label: '使用页面值',
                  value: '1',
                },
                {
                  label: '使用公式值',
                  value: '2',
                },
              ],
            },
          ],
        ],
      },
      // {
      //   key: 'qdgclJingDu',
      //   name: '清单工程量精度设置',
      //   options: [
      //     [
      //       {
      //         name: '清单工程量精度设置',
      //         field: 'qdgclJingDu',
      //         type: 'checkbox',
      //         value: false,
      //         disabled: false,
      //       },
      //       {
      //         name: '',
      //         field: 'qdUnitFormat',
      //         type: 'table',
      //         disabled: true,
      //         columns: [
      //           {
      //             title: '序号',
      //             customRender: ({ text, record, index }) => index,
      //           },
      //           { title: '计量单位', dataIndex: 'key' },
      //           {
      //             title: '精度',
      //             slot: true,
      //             dataIndex: 'value',
      //             // editRender: { autofocus: '.vxe-select' },
      //             // editable: ({ record: row }) => {
      //             //   return 'cellEditorSlot';
      //             // },
      //             selectList: [
      //               { label: '整数', value: 0 },
      //               { label: '小数点后1位', value: 1 },
      //               { label: '小数点后2位', value: 2 },
      //               { label: '小数点后3位', value: 3 },
      //               { label: '小数点后4位', value: 4 },
      //               { label: '小数点后5位', value: 5 },
      //               { label: '小数点后6位', value: 6 },
      //             ],
      //             customRender: ({ text, record, index }) => {
      //               if (text === 0) {
      //                 return '整数';
      //               }
      //               return `小数点后${text}位`;
      //             },
      //           },
      //         ],
      //         value: [],
      //       },
      //     ],
      //   ],
      // },
      // {
      //   key: 'degclJingDu',
      //   name: '定额工程量精度设置',
      //   options: [
      //     [
      //       {
      //         name: '定额工程量精度设置',
      //         field: 'degclJingDu',
      //         type: 'checkbox',
      //         value: false,
      //         disabled: false,
      //       },
      //       {
      //         name: '',
      //         field: 'deUnitFormat',
      //         type: 'table',
      //         disabled: true,
      //         columns: [
      //           {
      //             title: '序号',
      //             customRender: ({ text, record, index }) => index,
      //           },
      //           { title: '计量单位', dataIndex: 'key' },
      //           {
      //             title: '精度',
      //             slot: true,
      //             dataIndex: 'value',
      //             // editRender: { autofocus: '.vxe-select' },
      //             // editable: ({ record: row }) => {
      //             //   return 'cellEditorSlot';
      //             // },
      //             selectList: [
      //               { label: '整数', value: 0 },
      //               { label: '小数点后1位', value: 1 },
      //               { label: '小数点后2位', value: 2 },
      //               { label: '小数点后3位', value: 3 },
      //               { label: '小数点后4位', value: 4 },
      //               { label: '小数点后5位', value: 5 },
      //               { label: '小数点后6位', value: 6 },
      //             ],
      //             customRender: ({ text, record, index }) => {
      //               if (text === 0) {
      //                 return '整数';
      //               }
      //               return `小数点后${text}位`;
      //             },
      //           },
      //         ],
      //         value: [],
      //       },
      //     ],
      //   ],
      // },
      {
        key: 'zhanShi',
        name: '展示设置',
        options: [
          [
            {
              name: '插入定额数据时展示标准换算弹窗',
              field: 'standardConversionShowFlag',
              type: 'checkbox',
              value: true,
              disabled: false,
            },
            {
              name: '插入定额数据时展示主要材料价格设置弹窗',
              field: 'crdezszycljgsztk',
              type: 'checkbox',
              value: true,
              disabled: false,
            },
            {
              name: '插入定额数据时展示定额关联子目弹窗',
              field: 'crdezsdeglzmtk',
              type: 'checkbox',
              value: true,
              disabled: false,
            },
            {
              name: '推荐项目特征选中后自动关联组价方案',
              field: 'tjxmtzhzdglzjfa',
              type: 'checkbox',
              value: false,
            },
          ],
          [
            {
              name: '分部编码展示方式',
              field: 'bdCode',
              type: 'radio',
              value: '1',
              disabled: false,
              options: [
                {
                  label: '流水码（010101）',
                  value: '1',
                },
                {
                  label: '章节码（A.1.1）',
                  value: '2',
                },
              ],
            },
          ],
        ],
      },

      {
        key: 'biaoZhunHuanSuan',
        name: '标准换算设置',
        options: [
          [
            {
              name: '厚度/运距换算后带出子目合并到主子目',
              field: 'hdyjhsdczmhbdzzm',
              type: 'checkbox',
              value: true,
              disabled: true,
            },
            {
              name: '执行标准换算时主材、设备受系数影响',
              field: 'zxbzhszcsbsxsyx',
              type: 'checkbox',
              value: false,
            },
          ],
        ],
      },
      {
        key: 'chengXianXuanXiang',
        name: '呈现选项设置',
        options: [
          [
            {
              name: '隐藏补差项',
              field: 'ycbcx',
              type: 'checkbox',
              value: true,
              disabled: true,
            },
            {
              name: '子目编码显示换算串',
              field: 'zmbmxshsc',
              type: 'checkbox',
              value: true,
              disabled: true,
            },
            {
              name: '子目名称显示换算信息',
              field: 'zmmcxshsxx',
              type: 'checkbox',
              value: true,
              disabled: true,
            },

            {
              name: '锁定人材机消耗量',
              field: 'lockRcjResQty',
              type: 'checkbox',
              value: false,
              disabled: false,
            },
          ],
        ],
      },
      {
        key: 'baoBiaoXiangGuan',
        name: '报表相关设置',
        options: [
          [
            {
              name: '报表输出时，工程量为0的子目不显示',
              field: 'scgclw0zmbxs',
              type: 'checkbox',
              value: true,
              disabled: true,
            },
            {
              name: '报表单位工程名称显示单项名称+单位名称',
              field: 'dwgcmcxsdxmchdwmc',
              type: 'checkbox',
              value: true,
              disabled: true,
            },
          ],
        ],
      },
      {
        key: 'diQuTeXing',
        name: '地区特性',
        options: [
          [
            {
              name: '以系数计算的措施项目和机械台班中的人工单价参与调整',
              field: 'yxsjsdcsxmhjxtbzdrgdjcytz',
              type: 'checkbox',
              value: true,
              disabled: false,
            },
            {
              name: '税率可编辑',
              field: 'slkbj',
              type: 'checkbox',
              value: false,
            },
            {
              name: '除税系数是否可编辑',
              field: 'csxssfkbj',
              type: 'checkbox',
              value: true,
              disabled: true,
            },
            {
              name: '子目显示调价信息',
              field: 'zmxstjxx',
              type: 'checkbox',
              value: false,
              disabled: true,
            },
            {
              name: '市政设施维修养护工程执行中修(仅适用于 22 定额标准下市政维修养护单位工程)',
              field: 'szsswxyhzxzx22',
              type: 'checkbox',
              value: false,
              disabled: false,
            },
          ],
        ],
      },
      {
        key: 'computeJingDuSet',
        name: '计算精度设置',
        options: [
          {
            title: '费率精度设置',
            key: 'feeRate',
            options: [
              {
                name: '费率精度统一设置（%）',
                type: 'select',
                value: 4,
                field: 'fljdTysz',
                restoreDefaultFun: true, //有是否恢复默认功能
                selectList: [
                  { label: '整数', value: 0 },
                  { label: '小数点后1位', value: 1 },
                  { label: '小数点后2位', value: 2 },
                  { label: '小数点后3位', value: 3 },
                  { label: '小数点后4位', value: 4 },
                  { label: '小数点后5位', value: 5 },
                  { label: '小数点后6位', value: 6 },
                ],
              },
            ],
          },
          {
            title: '定额工程量精度设置',
            key: 'deSetting',
            options: [
              {
                type: 'radio',
                value: 0,
                field: 'degclJingDu',
                options: [
                  {
                    label: '子目工程量精度统一设置',
                    value: 0,
                    key: 'zmDeSetting',
                    options: [
                      {
                        type: 'select',
                        value: 5,
                        field: 'degclJingDuTysz',
                        disabled: false,
                        restoreDefaultFun: true, //有是否恢复默认功能
                        selectList: [
                          { label: '整数', value: 0 },
                          { label: '小数点后1位', value: 1 },
                          { label: '小数点后2位', value: 2 },
                          { label: '小数点后3位', value: 3 },
                          { label: '小数点后4位', value: 4 },
                          { label: '小数点后5位', value: 5 },
                          { label: '小数点后6位', value: 6 },
                        ],
                      },
                    ],
                  },
                  {
                    label: '按单位设置子目工程量精度',
                    key: 'dwDeSetting',
                    value: 1,
                    options: [
                      {
                        type: 'table',
                        disabled: true,
                        field: 'deUnitFormat',
                        restoreDefaultFun: true, //有是否恢复默认功能
                        columns: [
                          {
                            title: '序号',
                            width: 60,
                            customRender: ({ text, record, index }) => index + 1,
                          },
                          { title: '计量单位', align: 'center', dataIndex: 'key', width: 200 },
                          {
                            title: '精度',
                            slot: true,
                            align: 'center',
                            dataIndex: 'value',
                            width: 200,
                            selectList: [
                              { label: '整数', value: 0 },
                              { label: '小数点后1位', value: 1 },
                              { label: '小数点后2位', value: 2 },
                              { label: '小数点后3位', value: 3 },
                              { label: '小数点后4位', value: 4 },
                              { label: '小数点后5位', value: 5 },
                              { label: '小数点后6位', value: 6 },
                            ],
                            customRender: ({ text, record, index }) => {
                              if (text === 0) {
                                return '整数';
                              }
                              return `小数点后${text}位`;
                            },
                          },
                        ],
                        value: [
                          { key: '组', value: 0 },
                          { key: '宗', value: 0 },
                          { key: '元', value: 2 },
                          { key: '套', value: 0 },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          },
          {
            title: '清单工程量精度设置',
            key: 'qdSetting',
            options: [
              {
                type: 'radio',
                value: 0,
                field: 'qdgclJingDu',
                options: [
                  {
                    label: '清单工程量精度统一设置',
                    value: 0,
                    key: 'zmQdSetting',
                    options: [
                      {
                        type: 'select',
                        value: 4,
                        disabled: false,
                        field: 'qdgclJingDuTysz',
                        restoreDefaultFun: true, //有是否恢复默认功能
                        selectList: [
                          { label: '整数', value: 0 },
                          { label: '小数点后1位', value: 1 },
                          { label: '小数点后2位', value: 2 },
                          { label: '小数点后3位', value: 3 },
                          { label: '小数点后4位', value: 4 },
                          { label: '小数点后5位', value: 5 },
                          { label: '小数点后6位', value: 6 },
                        ],
                      },
                    ],
                  },
                  {
                    label: '按单位设置清单工程量精度',
                    key: 'dwQdSetting',
                    value: 1,
                    options: [
                      {
                        type: 'table',
                        disabled: true,
                        field: 'qdUnitFormat',
                        restoreDefaultFun: true, //有是否恢复默认功能
                        columns: [
                          {
                            title: '序号',
                            customRender: ({ text, record, index }) => index + 1,
                            width: 60,
                          },
                          { title: '计量单位', dataIndex: 'key', width: 200 },
                          {
                            title: '精度',
                            slot: true,
                            dataIndex: 'value',
                            width: 200,
                            selectList: [
                              { label: '整数', value: 0 },
                              { label: '小数点后1位', value: 1 },
                              { label: '小数点后2位', value: 2 },
                              { label: '小数点后3位', value: 3 },
                              { label: '小数点后4位', value: 4 },
                              { label: '小数点后5位', value: 5 },
                              { label: '小数点后6位', value: 6 },
                            ],
                            customRender: ({ text, record, index }) => {
                              if (text === 0) {
                                return '整数';
                              }
                              return `小数点后${text}位`;
                            },
                          },
                        ],
                        value: [
                          { key: '节', value: 2 },
                          { key: '口', value: 3 },
                          { key: '天', value: 4 },
                          { key: '间', value: 0 },
                          { key: '单元', value: 5 },
                          { key: '组件', value: 1 },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
  // {
  //   key: 'zmgclJingDu',
  //   name: '子目工程量精度设置',
  // },
  // {
  //   key: 'fyhzfyxjeJingDu',
  //   name: '费用汇总费用项金额精度设置',
  // },
]);

let objFormat = {
  qdUnitFormat: 'qdgclJingDu',
  deUnitFormat: 'degclJingDu',
};
const defaultSettingAllValue = info => {
  const diGuiFun = (options, info) => {
    for (let option of options) {
      if (Array.isArray(option)) {
        for (let opt of option) {
          if (Object.keys(info)?.includes(opt.field)) {
            opt.value = info[opt.field];
            if (opt.field === 'szsswxyhzxzx22') {
              opt.value = !!opt.value;
            }
            let childOptions = opt?.options || [];
            if (childOptions?.length > 0) {
              diGuiFun(childOptions, info);
            }
          }
        }
      }
      if (option.constructor === Object) {
        if (option?.options) {
          for (let opt of option.options) {
            if (Object.keys(info).includes(opt.field)) {
              if (['qdgclJingDu', 'degclJingDu'].includes(opt.field)) {
                opt.value =
                  typeof info[opt.field] === 'boolean'
                    ? info[opt.field]
                      ? 1
                      : 0
                    : info[opt.field];
                opt.options.map(a => {
                  a['options'][0].disabled = !(a.value == opt.value);
                });
              } else if (['deUnitFormat', 'qdUnitFormat'].includes(opt.field)) {
                let list = [];
                for (let key in info[opt.field]) {
                  list.push({ key: key, ...info[opt.field][key] });
                }
                console.log(info[opt.field], list);
                opt.value = [...list];
                // opt.disabled = !info[objFormat[opt.field]];
              } else {
                opt.value = info[opt.field];
              }
            }
            let childOptions = opt?.options || [];
            if (childOptions?.length > 0) {
              diGuiFun(childOptions, info);
            }
          }
        }
      }
    }
  };
  for (let item of treeData.value) {
    for (let child of item.children) {
      let options = child?.options || [];
      if (options?.length > 0) {
        diGuiFun(options, info);
      }
    }
  }
};
let tabKey = ref(['shuRu']);
let currentTreeInfo = ref(treeData.value[0].children[0].options);
const treeSelect = (keys, e) => {
  console.log('🌶setUpPopup.vue|262====>', keys);
  if (keys.length) {
    tabKey.value = keys;
    currentTreeInfo.value = e.node.options;
  } else {
    tabKey.value = [e.node.key];
  }
};

let globalConfigInfo = ref(null); // 获取全局设置信息
let beforeChangeParams = ref();

const getGlobalConfig = () => {
  globalConfigInfo.value = projectStore.globalConfigStructure;
  defaultSettingAllValue(projectStore.globalSettingInfo);
  beforeChangeParams.value = JSON.parse(JSON.stringify(getAllParams()));
};

const visible = ref(true);
const close = () => {
  emits('closePopup');
};
// key-value形式的所有参数
const getAllParams = () => {
  let params = {};
  const diGuiFun = options => {
    for (let option of options) {
      let optionList = option;
      if (!Array.isArray(option)) {
        params[option.field] = option.value;
        optionList = option.options || [];
      }
      for (let opt of optionList) {
        params[opt.field] = opt.value;
        if (opt.field === 'szsswxyhzxzx22') {
          params[opt.field] = !!params[opt.field];
        }
        let newList = opt?.options || [];
        if (newList.length > 0) {
          diGuiFun(newList);
        }
      }
    }
  };
  for (let item of treeData.value) {
    for (let child of item.children || []) {
      let options = child?.options || [];
      if (options?.length > 0) {
        diGuiFun(options);
      }
    }
  }
  console.log('🌶setUpPopup.vue|791====>全局参数', params);
  return params;
};
const pickDifference = (a, b) => {
  //对比出两个对象数组key值一样value不一样的数据
  const values = a.reduce((res, v) => res.add(Object.values(v).join()), new Set());
  return b.filter(v => !values.has(Object.values(v).join()));
};
// 值修改了的参数
const getChangeParams = () => {
  let params = getAllParams();
  let changeParams = {};
  for (let key in beforeChangeParams.value) {
    if (['deUnitFormat', 'qdUnitFormat'].includes(key)) {
      let unitFormat = {};
      let changeList = pickDifference(beforeChangeParams.value[key], params[key]);
      if (changeList?.length > 0) {
        changeList.map(i => {
          unitFormat[i.key] = { label: i.label, value: i.value };
        });
        changeParams[key] = { ...unitFormat };
      }
    } else if (params[key] !== beforeChangeParams.value[key]) {
      changeParams[key] = params[key];
      if (key === 'szsswxyhzxzx22' && changeParams[key] !== undefined) {
        // 如果是中修并且选中，则传值为2
        changeParams[key] = changeParams[key] ? '2' : '';
      }
    }
  }
  return changeParams;
};
const save = () => {
  let changeParams = getChangeParams();
  console.log('🚀 ~ save ~ changeParams:', changeParams, beforeChangeParams.value);
  if (Object.keys(changeParams).length > 0) {
    projectStore
      .SET_GLOBAL_CONFIG(projectStore.currentTreeGroupInfo?.constructId, changeParams)
      .then(res => {
        if (res.result) {
          message.success('设置成功');
          if (
            Object.keys(changeParams).some(item =>
              [
                'gcldw',
                'qdUnitFormat',
                'qdgclJingDu',
                'qdgclJingDuTysz',
                'degclJingDu',
                'deUnitFormat',
                'degclJingDuTysz',
                'bccjDesc',
                'zccjDesc',
                'szsswxyhzxzx22',
                'zxbzhszcsbsxsyx',
                'zhdjjsfs',
                'fljdTysz', //费率精度
                'qdzhjsfs', //清单综合合价计算方式
                'zhdjjsfs', //综合单价计算方式
              ].includes(item)
            )
          )
            refreshTableList();

          close();
        } else {
          message.error('设置失败');
        }
      });
  } else {
    close();
  }
};
const refreshTableList = () => {
  //刷新页面
  const levelType = projectStore.currentTreeInfo.levelType;
  if (projectStore.componentId === 'subItemProject') {
    projectStore.subItemProjectAutoPosition?.queryBranchDataById();
  }
  if (projectStore.componentId === 'measuresItem') {
    projectStore.measuresItemProjectAutoPosition?.queryBranchDataById();
  }
  if (projectStore.componentId === 'humanMachineSummary' && levelType === 3) {
    projectStore.summaryProjectAutoPosition?.getHumanMachineData();
  }
  if (projectStore.componentId === 'feeWithDrawalTable' && levelType === 3) {
    projectStore.feeWithDrawalAutoPosition?.refresh();
  }
  if (projectStore.componentId === 'CostAnalysis') {
    projectStore.costAnalysisComponentRef?.refreshList();
  }
  if (projectStore.componentId === 'summaryExpense') {
    projectStore.summaryExpenseGetList();
  }
};
// const getData = () => {
//   csProject
//     .getSetUp({ constructId: projectStore.currentTreeGroupInfo?.constructId })
//     .then(res => {
//       if (res.result) {
//         console.log('返回数据', res.result);
//         res.result.forEach(e => {
//           switch (e.paramsTag) {
//             case 'DEF_SAVE_PATH':
//               DEF_SAVE_PATH.value = e.content;
//               setSettingValue('qiTa', 'DEF_SAVE_PATH', e.content);
//               break;
//             case 'FILE_DOWNLOAD_PATH':
//               FILE_DOWNLOAD_PATH.value = e.content;
//               break;
//             case 'PROJECTATTR':
//               projectAttrVisible.value = true;
//               settingValue.value = e.content ? '1' : '';
//               break;
//             case 'RGFINMEASUREANDRPRICEINMECHANICALACTION':
//               rgfInMeasureAndRPriceInMechanicalVisible.value = true;
//               rgfInMeasureAndRPriceInMechanicalAction.value = e.content
//                 ? '1'
//                 : '';
//               break;
//             case 'mainRcjShowFlag':
//               mainRcjShowFlag.value = e.content;
//               break;
//             case 'standardConversionShowFlag':
//               standardConversionShowFlag.value = e.content;
//               break;
//           }
//         });
//         beforeChangeParams.value = JSON.parse(JSON.stringify(getAllParams()));
//       }
//     });
// };

// const setData = paramsTag => {
//   csProject.setSetUp({ paramsTag }).then(res => {
//     console.log(
//       '🚀 ~ file: setUpPopup.vue:69 ~ csProject.setSetUp ~ res:',
//       res
//     );
//     if (res.status == 200) {
//       switch (res.result) {
//         case 0:
//           console.log('点击了取消');
//           break;
//         case 1:
//           getData();
//           break;
//         default:
//           break;
//       }
//     }
//   });
// };

const openModal = () => {
  getGlobalConfig();
};
</script>

<style lang="scss" scoped>
:deep(.page-column-setting .vxe-modal--content) {
  background: #f5f5f5;
  padding: 20px !important;
}
:deep(.ant-checkbox-group) {
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  height: calc(60vh - 84px);
}
.page-setting {
  display: flex;
  height: 60vh;
  width: 100%;
  .tree-box {
    width: 160px;
    padding: 10px 0;
    border: 1px solid #e1e1e1;
    margin-right: 10px;
    background: rgba(255, 255, 255, 1);
  }
  .setting-box {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: calc(100% - 160px);
  }
  .content-box {
    flex: 1;
    padding: 12px;
    overflow: scroll;
    border: 1px solid #e1e1e1;
    background: rgba(255, 255, 255, 1);
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    // height: calc(60vh - 84px);
    height: 100%;
    :deep(.ant-checkbox-wrapper) {
      margin-left: 0;
      padding: 0;
    }
  }
}
.list-content {
  padding: 0;
  width: 100%;
  height: 100%;
  .item {
    margin-bottom: 23px;
    .title {
      display: block;
      font-size: 14px;
      font-weight: 400;
      color: #2a2a2a;
      margin-bottom: 20px;
    }
    .path {
      display: flex;
      align-items: center;
      .change {
        background: rgba(255, 255, 255, 0.39);
        border: 1px solid #bfbfbf;
        opacity: 1;
        border-radius: 3px;
        font-size: 14px;
        font-weight: 400;
        color: #2a2a2a;
        outline: none;
        padding: 6px 10px;
      }
      span {
        font-size: 14px;
        color: #898989;
        margin-left: 15px;
      }
    }
  }
}
.list-box {
  margin-bottom: 10px;
  padding: 5px 10px;
  border: 1px solid #e1e1e1;
}
.list-group {
  position: relative;
  margin-top: 18px;
  padding: 15px 10px 5px 10px;
  .title {
    top: -15px;
    left: 10px;
    padding: 0 2px;
    position: absolute;
    background-color: #fff;
    font-size: 14px;
    line-height: 30px;
    color: #2a2a2a;
  }
}
.page-setting-foot {
  padding-top: 12px;
  display: flex;
  justify-content: flex-end;
}
</style>
