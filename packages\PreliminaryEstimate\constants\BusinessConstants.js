class BusinessConstants{

  /**
   *  工程项目rcj  id
   * @type {number}
   */
  static PROJECT_RCJ_ID  = 1;

  /**
   *  单位项目rcj  id
   * @type {number}
   */
  static UNIT_RCJ_ID  = 2;

  /**
   *  单项rcj  id
   * @type {number}
   */
  static SINGLE_RCJ_ID  = 3;
  /**
   *  概算汇总 id
   * @type {number}
   */
  static PROJECT_GS_SUMMARY_ID = 3;

  /**
   *  预算书  id
   * @type {number}
   */
  static UNIT_YSH_ID  = 1003;

  /**
   *  单位工程-造价分析
   * @type {number}
   */
  static UNIT_ZJFX_ID  = 5;

  /**
   *  单位工程-三材表格列设置
   * @type {number}
   */
  static UNIT_SC_ID  = 7;

  /**
   *  单位工程-独立费
   * @type {number}
   */
  static UNIT_DLF_ID  = 6;



}
BusinessConstants.toString = () => 'BusinessConstants';
module.exports = BusinessConstants;