<!--
 * @Descripttion: 
-->
<template>
  <div class="rich-text">
    <div class="head">
      <div
        v-for="item in menuList"
        :key="item.key"
        :class="{ item: true, active: item.value === true && item.icon }"
        :data-key="item.key"
        @click="itemClick(item, $event)">
        <a-tooltip placement="bottom">
          <template #title v-if="item.tip">
            <span>{{ item.tip }}</span>
          </template>
          <div v-if="['cl', 'bg'].includes(item.key)" class="color-box">
            <input type="color" v-model="item.value" @change="colorChange" />
            <div class="color-icon">
              <icon-font :type="item.icon" class="icon-font"></icon-font>
              <span class="preview-color" :style="`background-color:${item.value}`"></span>
            </div>
          </div>
          <a-dropdown v-else-if="['ht', 'vt'].includes(item.key)" :trigger="['click']">
            <div class="align-box">
              <icon-font :type="item.icon" class="icon-font"></icon-font>
              <CaretDownOutlined />
            </div>
            <template #overlay>
              <a-menu @click="menuChange(item, $event)">
                <a-menu-item :key="t.value" v-for="t in item.options">
                  <div class="align-menu-item">
                    <CheckOutlined :style="`opacity: ${t.value === item.value ? 1 : 0}`" />
                    <icon-font :type="t.icon" class="icon-font"></icon-font>
                    <span class="text">{{ t.label }}</span>
                  </div>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
          <a-select
            v-else-if="item.key === 'ff'"
            v-model:value="item.value"
            @change="generateContentStyle"
            style="width: 60px">
            <template #suffixIcon>
              <CaretDownOutlined />
            </template>
            <a-select-option v-for="opt in item.options" :value="opt.value">
              {{ opt.label }}
            </a-select-option>
          </a-select>
          <div class="input-select" v-else-if="item.key === 'fs'">
            <a-auto-complete
              :bordered="true"
              style="min-width: 32px"
              v-model:value="item.value"
              @change="generateContentStyle"
              :options="item.options" />
            <CaretDownOutlined class="icon" />
          </div>
          <icon-font v-else-if="item.icon" :type="item.icon" class="icon-font"></icon-font>
        </a-tooltip>
      </div>
    </div>
    <div class="content" v-if="content" :style="contentStyle">
      <pre>{{ content }}</pre>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onBeforeUnmount, onMounted, shallowRef } from 'vue';
import { CaretDownOutlined, CheckOutlined } from '@ant-design/icons-vue';

const props = defineProps({
  content: {
    type: String,
    default: '',
  },
  defaultStyle: {
    type: Object,
    default: () => null,
  },
});

const emits = defineEmits(['update:defaultStyle']);
const colorChange = () => {
  generateContentStyle();
};
const getIcon = item => {
  return item.options.find(t => t.value === item.value)?.icon;
};
const menuChange = (item, event) => {
  item.value = event.key;
  item.icon = getIcon(item);
  generateContentStyle();
};
const menuList = ref([
  {
    key: 'bl',
    icon: 'icon-jiacu',
    value: false,
    tip: '粗体',
  },
  {
    key: 'it',
    icon: 'icon-qingxie',
    value: false,
    tip: '斜体',
  },
  {
    key: 'ul',
    icon: 'icon-xiahuaxian',
    value: false,
    tip: '下划线',
  },
  {
    key: 'ht',
    icon: 'icon-juzhong',
    value: 2,
    options: [
      {
        label: '左对齐',
        icon: 'icon-zuoduiqi',
        value: 1,
      },
      {
        label: '中间对齐',
        icon: 'icon-juzhong',
        value: 2,
      },
      {
        label: '右对齐',
        icon: 'icon-youduiqi',
        value: 3,
      },
    ],
  },
  {
    key: 'vt',
    icon: 'icon-juzhongduiqi',
    value: 2,
    options: [
      {
        label: '顶部对齐',
        icon: 'icon-dingbuduiqi',
        value: 1,
      },
      {
        label: '居中对齐',
        icon: 'icon-juzhongduiqi',
        value: 2,
      },
      {
        label: '底部对齐',
        icon: 'icon-dibuduiqi',
        value: 3,
      },
    ],
  },
  {
    key: 'ff',
    icon: '',
    value: '微软雅黑',
    tip: '字体',
    options: [
      { value: 'Arial', label: 'Arial' },
      { value: 'Times New Roman', label: 'Times New Roman' },
      { value: 'Tahoma', label: 'Tahoma' },
      { value: 'Verdana', label: 'Verdana' },
      { value: 'Microsoft YaHei', label: '微软雅黑' },
      { value: 'SimSun', label: '宋体' },
      { value: 'SimHei', label: '黑体' },
      { value: 'Kaiti', label: '楷体' },
      { value: 'FangSong', label: '仿宋' },
      { value: 'NSimSun', label: '新宋体' },
      { value: 'STXingkai', label: '华文行楷' },
      { value: 'STLiti', label: '华文隶书' },
      { value: 'STXinwei', label: '华文新魏' },
    ],
  },
  {
    key: 'fs',
    icon: '',
    value: 12,
    tip: '字号',
    options: [
      { value: 6 },
      { value: 7 },
      { value: 8 },
      { value: 9 },
      { value: 10 },
      { value: 11 },
      { value: 12 },
      { value: 14 },
      { value: 16 },
      { value: 18 },
      { value: 20 },
      { value: 24 },
      { value: 30 },
      { value: 36 },
    ],
  },
  {
    key: 'cl',
    icon: 'icon-ziti',
    value: 'rgb(0,0,0)',
    tip: '文本颜色',
  },
  {
    key: 'bg',
    icon: 'icon-tianchong',
    value: 'rgb(255,255,255)',
    tip: '单元格颜色',
  },
]);
const itemClick = item => {
  if (['cl', 'bg'].includes(item.key)) {
    return;
  }
  if (item.icon && !item.options) {
    item.value = !item.value;
    generateContentStyle();
  }
  console.log('🚀 ~ itemClick ~ item:', item);
};
let contentStyle = ref({});

const initMenuList = defaultStyle => {
  console.log('🚀 ~ initMenuList ~ defaultStyle:', defaultStyle);
  if (!defaultStyle) return;
  menuList.value.forEach(item => {
    if (['bl', 'it'].includes(item.key)) {
      item.value = !!defaultStyle[item.key];
    } else if (item.key === 'ul') {
      item.value = !!defaultStyle[item.key]?.s;
    } else if (['cl', 'bg'].includes(item.key) && defaultStyle[item.key]?.rgb) {
      item.value = defaultStyle[item.key]['rgb'];
    } else {
      if (defaultStyle[item.key] === undefined) return;
      item.value = defaultStyle[item.key];
    }
  });
};
const getUniverStyle = () => {
  let styleObj = {};
  menuList.value.forEach(item => {
    if (['bl', 'it'].includes(item.key)) {
      styleObj[item.key] = item.value ? 1 : 0;
    } else if (item.key === 'ul') {
      styleObj[item.key] = {
        cl: { rgb: 'rgb(0,0,0)' },
        s: item.value ? 1 : 0,
      };
    } else if (['cl', 'bg'].includes(item.key)) {
      styleObj[item.key] = { rgb: hexToRgb(item.value) };
    } else {
      styleObj[item.key] = item.value;
    }
  });
  return {
    ...props.defaultStyle,
    ...styleObj,
  };
};
const htMap = {
  1: 'left',
  2: 'center',
  3: 'right',
};
const vtMap = {
  1: 'top',
  2: 'middle',
  3: 'bottom',
};
const generateContentStyle = (update = true) => {
  const style = {};
  menuList.value.forEach(item => {
    if (['bl'].includes(item.key)) {
      style.fontWeight = item.value ? 'bold' : 'normal';
    } else if (['ff'].includes(item.key)) {
      style.fontFamily = item.value;
    } else if (item.key === 'ul') {
      style['text-decoration-line'] = item.value ? 'underline' : 'none';
    } else if (item.key === 'it') {
      style['fontStyle'] = item.value ? 'oblique' : 'normal';
    } else if (item.key === 'ht' && item.value) {
      style.textAlign = htMap[item.value];
    } else if (item.key === 'vt' && item.value) {
      style.verticalAlign = vtMap[item.value];
    } else if (item.key === 'cl' && item.value) {
      style.color = item.value;
    } else if (item.key === 'bg' && item.value) {
      style.backgroundColor = item.value;
    } else if (item.key === 'fs') {
      style.fontSize = item.value + 'px';
    }
  });
  contentStyle.value = style;
  if (update) {
    emits('update:defaultStyle', getUniverStyle());
  }
};
function hexToRgb(hex) {
  hex = hex.replace(/^#?([a-f\d])([a-f\d])([a-f\d])$/i, (m, r, g, b) => r + r + g + g + b + b);
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  if (!result) return hex;
  return `rgb(${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)})`;
}
onMounted(() => {
  initMenuList(props.defaultStyle);
  generateContentStyle(false);
});
onBeforeUnmount(() => {});
</script>
<style lang="scss" scoped>
.color-box {
  position: relative;
  input {
    width: 100%;
    height: 100%;
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
  }
  .color-icon {
    position: relative;
    font-size: 14px;
    .preview-color {
      position: absolute;
      bottom: 3px;
      left: 0;
      width: 14px;
      height: 4px;
      border-radius: 2px;
    }
  }
}
.align-menu-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  .anticon-check {
    opacity: 0;
    color: #287cfa;
  }
  span:nth-child(2) {
    padding: 0 7px;
  }
}
.align-box {
  width: 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.head {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  :deep(.ant-select-selector) {
    border: none !important;
    padding: 0 !important;
    box-shadow: none !important;
  }
  :deep(.ant-select-arrow) {
    right: 1px !important;
    color: #535353 !important;
  }
  :deep(.ant-select-selection-search) {
    left: 0;
    right: 0;
  }
}
.item {
  padding: 0 2px;
  margin: 0 2px;
  cursor: pointer;
  font-size: 12px;
  &.active {
    background: rgba($color: #000000, $alpha: 0.3);
  }
}
.input-select {
  position: relative;
  .icon {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}
.content {
  border: 1px solid #d9d9d9;
  line-height: 1.5;
  padding: 15px;
}
pre {
  font-family: inherit;
  white-space: pre-wrap;
  overflow-wrap: break-word;
  box-sizing: border-box;
  font-size: inherit;
  margin: 0;
}
</style>
